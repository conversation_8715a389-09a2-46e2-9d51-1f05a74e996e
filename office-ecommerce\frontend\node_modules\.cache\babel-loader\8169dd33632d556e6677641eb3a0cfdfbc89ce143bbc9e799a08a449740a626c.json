{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\demo\\\\GLBUploaderDemo.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport * as THREE from 'three';\nimport GLBUploader from '../3d/GLBUploader';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GLBUploaderDemo = () => {\n  _s();\n  const mountRef = useRef(null);\n  const sceneRef = useRef(null);\n  const rendererRef = useRef(null);\n  const productRef = useRef(null);\n  const animationIdRef = useRef(null);\n  const [uploadedModel, setUploadedModel] = useState(null);\n\n  // Initialize 3D scene\n  useEffect(() => {\n    if (!mountRef.current) return;\n\n    // Scene setup\n    const scene = new THREE.Scene();\n    scene.background = new THREE.Color(0xf5f5f5);\n    sceneRef.current = scene;\n\n    // Camera setup\n    const camera = new THREE.PerspectiveCamera(75, mountRef.current.clientWidth / mountRef.current.clientHeight, 0.1, 1000);\n    camera.position.set(4, 3, 4);\n    camera.lookAt(0, 0, 0);\n\n    // Renderer setup\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);\n    renderer.shadowMap.enabled = true;\n    renderer.shadowMap.type = THREE.PCFSoftShadowMap;\n    mountRef.current.appendChild(renderer.domElement);\n    rendererRef.current = renderer;\n\n    // Lighting\n    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);\n    scene.add(ambientLight);\n    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);\n    directionalLight.position.set(5, 5, 5);\n    directionalLight.castShadow = true;\n    scene.add(directionalLight);\n\n    // Create default cube\n    createDefaultProduct(scene);\n\n    // Animation loop\n    const animate = () => {\n      animationIdRef.current = requestAnimationFrame(animate);\n\n      // Rotate the product\n      if (productRef.current) {\n        productRef.current.rotation.y += 0.01;\n      }\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // Handle resize\n    const handleResize = () => {\n      if (!mountRef.current) return;\n      const width = mountRef.current.clientWidth;\n      const height = mountRef.current.clientHeight;\n      camera.aspect = width / height;\n      camera.updateProjectionMatrix();\n      renderer.setSize(width, height);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (animationIdRef.current) {\n        cancelAnimationFrame(animationIdRef.current);\n      }\n      if (mountRef.current && renderer.domElement) {\n        mountRef.current.removeChild(renderer.domElement);\n      }\n      renderer.dispose();\n    };\n  }, []);\n  const createDefaultProduct = scene => {\n    // Create a simple default cube\n    const geometry = new THREE.BoxGeometry(2, 2, 2);\n    const material = new THREE.MeshStandardMaterial({\n      color: 0x6B7280,\n      roughness: 0.7,\n      metalness: 0.1\n    });\n    const cube = new THREE.Mesh(geometry, material);\n    cube.castShadow = true;\n    cube.receiveShadow = true;\n    productRef.current = cube;\n    scene.add(cube);\n  };\n  const handleModelLoad = (model, modelInfo) => {\n    if (model) {\n      setUploadedModel({\n        model,\n        info: modelInfo\n      });\n      console.log('Model loaded:', modelInfo);\n    } else {\n      // Model was removed, recreate default product\n      setUploadedModel(null);\n      if (sceneRef.current) {\n        createDefaultProduct(sceneRef.current);\n      }\n    }\n  };\n  const handleUploadError = error => {\n    console.error('Upload error:', error);\n    alert(error);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"GLB Uploader Demo\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"This demo shows how to integrate the GLB uploader with a 3D scene.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: '1fr 400px',\n        gap: '2rem',\n        marginTop: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#f8f9fa',\n          borderRadius: '12px',\n          padding: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"3D Preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: mountRef,\n          style: {\n            width: '100%',\n            height: '400px',\n            background: '#fff',\n            borderRadius: '8px',\n            border: '1px solid #e5e7eb'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(GLBUploader, {\n          onModelLoad: handleModelLoad,\n          onError: handleUploadError,\n          currentScene: sceneRef.current,\n          productRef: productRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 21\n        }, this), uploadedModel && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1rem',\n            padding: '1rem',\n            background: '#d4edda',\n            borderRadius: '8px',\n            border: '1px solid #c3e6cb'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 0.5rem 0',\n              color: '#155724'\n            },\n            children: \"Model Successfully Loaded!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              color: '#155724',\n              fontSize: '0.9rem'\n            },\n            children: \"Your custom 3D model is now displayed in the viewer above.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1rem',\n            padding: '1rem',\n            background: '#e3f2fd',\n            borderRadius: '8px',\n            border: '1px solid #bbdefb'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 0.5rem 0',\n              color: '#1565c0'\n            },\n            children: \"How to Use:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n            style: {\n              margin: 0,\n              paddingLeft: '1.25rem',\n              color: '#1565c0',\n              fontSize: '0.9rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Click \\\"Upload GLB/GLTF\\\" button\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Select your 3D model file\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Wait for the upload to complete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Your model will replace the default cube\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Use \\\"Remove\\\" to go back to default\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 9\n  }, this);\n};\n_s(GLBUploaderDemo, \"tdhjPvG6ZABJPe0CK+BPjdwWyak=\");\n_c = GLBUploaderDemo;\nexport default GLBUploaderDemo;\nvar _c;\n$RefreshReg$(_c, \"GLBUploaderDemo\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "THREE", "GLBUploader", "jsxDEV", "_jsxDEV", "GLBUploaderDemo", "_s", "mountRef", "sceneRef", "rendererRef", "productRef", "animationIdRef", "uploadedModel", "setUploadedModel", "current", "scene", "Scene", "background", "Color", "camera", "PerspectiveCamera", "clientWidth", "clientHeight", "position", "set", "lookAt", "renderer", "WebGLRenderer", "antialias", "setSize", "shadowMap", "enabled", "type", "PCFSoftShadowMap", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "add", "directionalLight", "DirectionalLight", "<PERSON><PERSON><PERSON><PERSON>", "createDefaultProduct", "animate", "requestAnimationFrame", "rotation", "y", "render", "handleResize", "width", "height", "aspect", "updateProjectionMatrix", "window", "addEventListener", "removeEventListener", "cancelAnimationFrame", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "geometry", "BoxGeometry", "material", "MeshStandardMaterial", "color", "roughness", "metalness", "cube", "<PERSON><PERSON>", "receiveShadow", "handleModelLoad", "model", "modelInfo", "info", "console", "log", "handleUploadError", "error", "alert", "style", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "gridTemplateColumns", "gap", "marginTop", "borderRadius", "ref", "border", "onModelLoad", "onError", "currentScene", "margin", "fontSize", "paddingLeft", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/demo/GLBUploaderDemo.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport * as THREE from 'three';\nimport GLBUploader from '../3d/GLBUploader';\n\nconst GLBUploaderDemo = () => {\n    const mountRef = useRef(null);\n    const sceneRef = useRef(null);\n    const rendererRef = useRef(null);\n    const productRef = useRef(null);\n    const animationIdRef = useRef(null);\n    const [uploadedModel, setUploadedModel] = useState(null);\n\n    // Initialize 3D scene\n    useEffect(() => {\n        if (!mountRef.current) return;\n\n        // Scene setup\n        const scene = new THREE.Scene();\n        scene.background = new THREE.Color(0xf5f5f5);\n        sceneRef.current = scene;\n\n        // Camera setup\n        const camera = new THREE.PerspectiveCamera(\n            75,\n            mountRef.current.clientWidth / mountRef.current.clientHeight,\n            0.1,\n            1000\n        );\n        camera.position.set(4, 3, 4);\n        camera.lookAt(0, 0, 0);\n\n        // Renderer setup\n        const renderer = new THREE.WebGLRenderer({ antialias: true });\n        renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);\n        renderer.shadowMap.enabled = true;\n        renderer.shadowMap.type = THREE.PCFSoftShadowMap;\n        mountRef.current.appendChild(renderer.domElement);\n        rendererRef.current = renderer;\n\n        // Lighting\n        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);\n        scene.add(ambientLight);\n\n        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);\n        directionalLight.position.set(5, 5, 5);\n        directionalLight.castShadow = true;\n        scene.add(directionalLight);\n\n        // Create default cube\n        createDefaultProduct(scene);\n\n        // Animation loop\n        const animate = () => {\n            animationIdRef.current = requestAnimationFrame(animate);\n            \n            // Rotate the product\n            if (productRef.current) {\n                productRef.current.rotation.y += 0.01;\n            }\n            \n            renderer.render(scene, camera);\n        };\n        animate();\n\n        // Handle resize\n        const handleResize = () => {\n            if (!mountRef.current) return;\n            const width = mountRef.current.clientWidth;\n            const height = mountRef.current.clientHeight;\n            camera.aspect = width / height;\n            camera.updateProjectionMatrix();\n            renderer.setSize(width, height);\n        };\n\n        window.addEventListener('resize', handleResize);\n\n        return () => {\n            window.removeEventListener('resize', handleResize);\n            if (animationIdRef.current) {\n                cancelAnimationFrame(animationIdRef.current);\n            }\n            if (mountRef.current && renderer.domElement) {\n                mountRef.current.removeChild(renderer.domElement);\n            }\n            renderer.dispose();\n        };\n    }, []);\n\n    const createDefaultProduct = (scene) => {\n        // Create a simple default cube\n        const geometry = new THREE.BoxGeometry(2, 2, 2);\n        const material = new THREE.MeshStandardMaterial({ \n            color: 0x6B7280,\n            roughness: 0.7,\n            metalness: 0.1\n        });\n        const cube = new THREE.Mesh(geometry, material);\n        cube.castShadow = true;\n        cube.receiveShadow = true;\n        \n        productRef.current = cube;\n        scene.add(cube);\n    };\n\n    const handleModelLoad = (model, modelInfo) => {\n        if (model) {\n            setUploadedModel({ model, info: modelInfo });\n            console.log('Model loaded:', modelInfo);\n        } else {\n            // Model was removed, recreate default product\n            setUploadedModel(null);\n            if (sceneRef.current) {\n                createDefaultProduct(sceneRef.current);\n            }\n        }\n    };\n\n    const handleUploadError = (error) => {\n        console.error('Upload error:', error);\n        alert(error);\n    };\n\n    return (\n        <div style={{ padding: '2rem' }}>\n            <h2>GLB Uploader Demo</h2>\n            <p>This demo shows how to integrate the GLB uploader with a 3D scene.</p>\n            \n            <div style={{ display: 'grid', gridTemplateColumns: '1fr 400px', gap: '2rem', marginTop: '2rem' }}>\n                {/* 3D Viewer */}\n                <div style={{ background: '#f8f9fa', borderRadius: '12px', padding: '1rem' }}>\n                    <h3>3D Preview</h3>\n                    <div \n                        ref={mountRef} \n                        style={{ \n                            width: '100%', \n                            height: '400px', \n                            background: '#fff', \n                            borderRadius: '8px',\n                            border: '1px solid #e5e7eb'\n                        }}\n                    />\n                </div>\n\n                {/* Upload Panel */}\n                <div>\n                    <GLBUploader\n                        onModelLoad={handleModelLoad}\n                        onError={handleUploadError}\n                        currentScene={sceneRef.current}\n                        productRef={productRef}\n                    />\n                    \n                    {uploadedModel && (\n                        <div style={{ \n                            marginTop: '1rem', \n                            padding: '1rem', \n                            background: '#d4edda', \n                            borderRadius: '8px',\n                            border: '1px solid #c3e6cb'\n                        }}>\n                            <h4 style={{ margin: '0 0 0.5rem 0', color: '#155724' }}>\n                                Model Successfully Loaded!\n                            </h4>\n                            <p style={{ margin: 0, color: '#155724', fontSize: '0.9rem' }}>\n                                Your custom 3D model is now displayed in the viewer above.\n                            </p>\n                        </div>\n                    )}\n\n                    <div style={{ \n                        marginTop: '1rem', \n                        padding: '1rem', \n                        background: '#e3f2fd', \n                        borderRadius: '8px',\n                        border: '1px solid #bbdefb'\n                    }}>\n                        <h4 style={{ margin: '0 0 0.5rem 0', color: '#1565c0' }}>\n                            How to Use:\n                        </h4>\n                        <ol style={{ margin: 0, paddingLeft: '1.25rem', color: '#1565c0', fontSize: '0.9rem' }}>\n                            <li>Click \"Upload GLB/GLTF\" button</li>\n                            <li>Select your 3D model file</li>\n                            <li>Wait for the upload to complete</li>\n                            <li>Your model will replace the default cube</li>\n                            <li>Use \"Remove\" to go back to default</li>\n                        </ol>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default GLBUploaderDemo;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,QAAQ,GAAGR,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMS,QAAQ,GAAGT,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMU,WAAW,GAAGV,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMW,UAAU,GAAGX,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMY,cAAc,GAAGZ,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACAE,SAAS,CAAC,MAAM;IACZ,IAAI,CAACO,QAAQ,CAACO,OAAO,EAAE;;IAEvB;IACA,MAAMC,KAAK,GAAG,IAAId,KAAK,CAACe,KAAK,CAAC,CAAC;IAC/BD,KAAK,CAACE,UAAU,GAAG,IAAIhB,KAAK,CAACiB,KAAK,CAAC,QAAQ,CAAC;IAC5CV,QAAQ,CAACM,OAAO,GAAGC,KAAK;;IAExB;IACA,MAAMI,MAAM,GAAG,IAAIlB,KAAK,CAACmB,iBAAiB,CACtC,EAAE,EACFb,QAAQ,CAACO,OAAO,CAACO,WAAW,GAAGd,QAAQ,CAACO,OAAO,CAACQ,YAAY,EAC5D,GAAG,EACH,IACJ,CAAC;IACDH,MAAM,CAACI,QAAQ,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BL,MAAM,CAACM,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;IAEtB;IACA,MAAMC,QAAQ,GAAG,IAAIzB,KAAK,CAAC0B,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACtB,QAAQ,CAACO,OAAO,CAACO,WAAW,EAAEd,QAAQ,CAACO,OAAO,CAACQ,YAAY,CAAC;IAC7EI,QAAQ,CAACI,SAAS,CAACC,OAAO,GAAG,IAAI;IACjCL,QAAQ,CAACI,SAAS,CAACE,IAAI,GAAG/B,KAAK,CAACgC,gBAAgB;IAChD1B,QAAQ,CAACO,OAAO,CAACoB,WAAW,CAACR,QAAQ,CAACS,UAAU,CAAC;IACjD1B,WAAW,CAACK,OAAO,GAAGY,QAAQ;;IAE9B;IACA,MAAMU,YAAY,GAAG,IAAInC,KAAK,CAACoC,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC1DtB,KAAK,CAACuB,GAAG,CAACF,YAAY,CAAC;IAEvB,MAAMG,gBAAgB,GAAG,IAAItC,KAAK,CAACuC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IAClED,gBAAgB,CAAChB,QAAQ,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtCe,gBAAgB,CAACE,UAAU,GAAG,IAAI;IAClC1B,KAAK,CAACuB,GAAG,CAACC,gBAAgB,CAAC;;IAE3B;IACAG,oBAAoB,CAAC3B,KAAK,CAAC;;IAE3B;IACA,MAAM4B,OAAO,GAAGA,CAAA,KAAM;MAClBhC,cAAc,CAACG,OAAO,GAAG8B,qBAAqB,CAACD,OAAO,CAAC;;MAEvD;MACA,IAAIjC,UAAU,CAACI,OAAO,EAAE;QACpBJ,UAAU,CAACI,OAAO,CAAC+B,QAAQ,CAACC,CAAC,IAAI,IAAI;MACzC;MAEApB,QAAQ,CAACqB,MAAM,CAAChC,KAAK,EAAEI,MAAM,CAAC;IAClC,CAAC;IACDwB,OAAO,CAAC,CAAC;;IAET;IACA,MAAMK,YAAY,GAAGA,CAAA,KAAM;MACvB,IAAI,CAACzC,QAAQ,CAACO,OAAO,EAAE;MACvB,MAAMmC,KAAK,GAAG1C,QAAQ,CAACO,OAAO,CAACO,WAAW;MAC1C,MAAM6B,MAAM,GAAG3C,QAAQ,CAACO,OAAO,CAACQ,YAAY;MAC5CH,MAAM,CAACgC,MAAM,GAAGF,KAAK,GAAGC,MAAM;MAC9B/B,MAAM,CAACiC,sBAAsB,CAAC,CAAC;MAC/B1B,QAAQ,CAACG,OAAO,CAACoB,KAAK,EAAEC,MAAM,CAAC;IACnC,CAAC;IAEDG,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IAE/C,OAAO,MAAM;MACTK,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;MAClD,IAAIrC,cAAc,CAACG,OAAO,EAAE;QACxB0C,oBAAoB,CAAC7C,cAAc,CAACG,OAAO,CAAC;MAChD;MACA,IAAIP,QAAQ,CAACO,OAAO,IAAIY,QAAQ,CAACS,UAAU,EAAE;QACzC5B,QAAQ,CAACO,OAAO,CAAC2C,WAAW,CAAC/B,QAAQ,CAACS,UAAU,CAAC;MACrD;MACAT,QAAQ,CAACgC,OAAO,CAAC,CAAC;IACtB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMhB,oBAAoB,GAAI3B,KAAK,IAAK;IACpC;IACA,MAAM4C,QAAQ,GAAG,IAAI1D,KAAK,CAAC2D,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/C,MAAMC,QAAQ,GAAG,IAAI5D,KAAK,CAAC6D,oBAAoB,CAAC;MAC5CC,KAAK,EAAE,QAAQ;MACfC,SAAS,EAAE,GAAG;MACdC,SAAS,EAAE;IACf,CAAC,CAAC;IACF,MAAMC,IAAI,GAAG,IAAIjE,KAAK,CAACkE,IAAI,CAACR,QAAQ,EAAEE,QAAQ,CAAC;IAC/CK,IAAI,CAACzB,UAAU,GAAG,IAAI;IACtByB,IAAI,CAACE,aAAa,GAAG,IAAI;IAEzB1D,UAAU,CAACI,OAAO,GAAGoD,IAAI;IACzBnD,KAAK,CAACuB,GAAG,CAAC4B,IAAI,CAAC;EACnB,CAAC;EAED,MAAMG,eAAe,GAAGA,CAACC,KAAK,EAAEC,SAAS,KAAK;IAC1C,IAAID,KAAK,EAAE;MACPzD,gBAAgB,CAAC;QAAEyD,KAAK;QAAEE,IAAI,EAAED;MAAU,CAAC,CAAC;MAC5CE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEH,SAAS,CAAC;IAC3C,CAAC,MAAM;MACH;MACA1D,gBAAgB,CAAC,IAAI,CAAC;MACtB,IAAIL,QAAQ,CAACM,OAAO,EAAE;QAClB4B,oBAAoB,CAAClC,QAAQ,CAACM,OAAO,CAAC;MAC1C;IACJ;EACJ,CAAC;EAED,MAAM6D,iBAAiB,GAAIC,KAAK,IAAK;IACjCH,OAAO,CAACG,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACrCC,KAAK,CAACD,KAAK,CAAC;EAChB,CAAC;EAED,oBACIxE,OAAA;IAAK0E,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC5B5E,OAAA;MAAA4E,QAAA,EAAI;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC1BhF,OAAA;MAAA4E,QAAA,EAAG;IAAkE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAEzEhF,OAAA;MAAK0E,KAAK,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAEC,mBAAmB,EAAE,WAAW;QAAEC,GAAG,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAR,QAAA,gBAE9F5E,OAAA;QAAK0E,KAAK,EAAE;UAAE7D,UAAU,EAAE,SAAS;UAAEwE,YAAY,EAAE,MAAM;UAAEV,OAAO,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACzE5E,OAAA;UAAA4E,QAAA,EAAI;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnBhF,OAAA;UACIsF,GAAG,EAAEnF,QAAS;UACduE,KAAK,EAAE;YACH7B,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,OAAO;YACfjC,UAAU,EAAE,MAAM;YAClBwE,YAAY,EAAE,KAAK;YACnBE,MAAM,EAAE;UACZ;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNhF,OAAA;QAAA4E,QAAA,gBACI5E,OAAA,CAACF,WAAW;UACR0F,WAAW,EAAEvB,eAAgB;UAC7BwB,OAAO,EAAElB,iBAAkB;UAC3BmB,YAAY,EAAEtF,QAAQ,CAACM,OAAQ;UAC/BJ,UAAU,EAAEA;QAAW;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,EAEDxE,aAAa,iBACVR,OAAA;UAAK0E,KAAK,EAAE;YACRU,SAAS,EAAE,MAAM;YACjBT,OAAO,EAAE,MAAM;YACf9D,UAAU,EAAE,SAAS;YACrBwE,YAAY,EAAE,KAAK;YACnBE,MAAM,EAAE;UACZ,CAAE;UAAAX,QAAA,gBACE5E,OAAA;YAAI0E,KAAK,EAAE;cAAEiB,MAAM,EAAE,cAAc;cAAEhC,KAAK,EAAE;YAAU,CAAE;YAAAiB,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhF,OAAA;YAAG0E,KAAK,EAAE;cAAEiB,MAAM,EAAE,CAAC;cAAEhC,KAAK,EAAE,SAAS;cAAEiC,QAAQ,EAAE;YAAS,CAAE;YAAAhB,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACR,eAEDhF,OAAA;UAAK0E,KAAK,EAAE;YACRU,SAAS,EAAE,MAAM;YACjBT,OAAO,EAAE,MAAM;YACf9D,UAAU,EAAE,SAAS;YACrBwE,YAAY,EAAE,KAAK;YACnBE,MAAM,EAAE;UACZ,CAAE;UAAAX,QAAA,gBACE5E,OAAA;YAAI0E,KAAK,EAAE;cAAEiB,MAAM,EAAE,cAAc;cAAEhC,KAAK,EAAE;YAAU,CAAE;YAAAiB,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhF,OAAA;YAAI0E,KAAK,EAAE;cAAEiB,MAAM,EAAE,CAAC;cAAEE,WAAW,EAAE,SAAS;cAAElC,KAAK,EAAE,SAAS;cAAEiC,QAAQ,EAAE;YAAS,CAAE;YAAAhB,QAAA,gBACnF5E,OAAA;cAAA4E,QAAA,EAAI;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvChF,OAAA;cAAA4E,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClChF,OAAA;cAAA4E,QAAA,EAAI;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxChF,OAAA;cAAA4E,QAAA,EAAI;YAAwC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDhF,OAAA;cAAA4E,QAAA,EAAI;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC9E,EAAA,CA3LID,eAAe;AAAA6F,EAAA,GAAf7F,eAAe;AA6LrB,eAAeA,eAAe;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}