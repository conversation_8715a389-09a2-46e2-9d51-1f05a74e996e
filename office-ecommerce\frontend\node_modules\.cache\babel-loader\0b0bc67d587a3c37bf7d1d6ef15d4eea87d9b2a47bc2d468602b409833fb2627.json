{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './hooks/useAuth';\nimport { CartProvider } from './contexts/CartContext';\nimport Header from './components/common/Header';\nimport Footer from './components/common/Footer';\nimport Home from './pages/Home';\nimport Login from './pages/Login';\nimport ProductCatalog from './pages/ProductCatalog';\nimport ProductDetail from './pages/ProductDetail';\nimport Cart from './pages/Cart';\nimport Payment from './pages/Payment';\nimport About from './pages/About';\nimport Gallery from './pages/Gallery';\nimport ProductCardDemo from './components/demo/ProductCardDemo';\nimport './styles/globals.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(CartProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"App\",\n          children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 27,\n                  columnNumber: 58\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/login\",\n                element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 28,\n                  columnNumber: 63\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/products\",\n                element: /*#__PURE__*/_jsxDEV(ProductCatalog, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 29,\n                  columnNumber: 66\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/product/:id\",\n                element: /*#__PURE__*/_jsxDEV(ProductDetail, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 69\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/products/:id\",\n                element: /*#__PURE__*/_jsxDEV(ProductDetail, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 70\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/cart\",\n                element: /*#__PURE__*/_jsxDEV(Cart, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 62\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/payment\",\n                element: /*#__PURE__*/_jsxDEV(Payment, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 33,\n                  columnNumber: 65\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/about\",\n                element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 63\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/gallery\",\n                element: /*#__PURE__*/_jsxDEV(Gallery, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 35,\n                  columnNumber: 65\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/demo\",\n                element: /*#__PURE__*/_jsxDEV(ProductCardDemo, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 36,\n                  columnNumber: 62\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/glb-demo\",\n                element: /*#__PURE__*/_jsxDEV(GLBUploaderDemo, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 37,\n                  columnNumber: 66\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 9\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "CartProvider", "Header", "Footer", "Home", "<PERSON><PERSON>", "ProductCatalog", "ProductDetail", "<PERSON><PERSON>", "Payment", "About", "Gallery", "ProductCardDemo", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "GLBUploaderDemo", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './hooks/useAuth';\nimport { CartProvider } from './contexts/CartContext';\nimport Header from './components/common/Header';\nimport Footer from './components/common/Footer';\nimport Home from './pages/Home';\nimport Login from './pages/Login';\nimport ProductCatalog from './pages/ProductCatalog';\nimport ProductDetail from './pages/ProductDetail';\nimport Cart from './pages/Cart';\nimport Payment from './pages/Payment';\nimport About from './pages/About';\nimport Gallery from './pages/Gallery';\nimport ProductCardDemo from './components/demo/ProductCardDemo';\nimport './styles/globals.css';\n\nfunction App() {\n    return (\n        <AuthProvider>\n            <CartProvider>\n                <Router>\n                    <div className=\"App\">\n                        <Header />\n                        <main>\n                            <Routes>\n                                <Route path=\"/\" element={<Home />} />\n                                <Route path=\"/login\" element={<Login />} />\n                                <Route path=\"/products\" element={<ProductCatalog />} />\n                                <Route path=\"/product/:id\" element={<ProductDetail />} />\n                                <Route path=\"/products/:id\" element={<ProductDetail />} />\n                                <Route path=\"/cart\" element={<Cart />} />\n                                <Route path=\"/payment\" element={<Payment />} />\n                                <Route path=\"/about\" element={<About />} />\n                                <Route path=\"/gallery\" element={<Gallery />} />\n                                <Route path=\"/demo\" element={<ProductCardDemo />} />\n                                <Route path=\"/glb-demo\" element={<GLBUploaderDemo />} />\n                            </Routes>\n                        </main>\n                        <Footer />\n                    </div>\n                </Router>\n            </CartProvider>\n        </AuthProvider>\n    );\n}\n\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,eAAe,MAAM,mCAAmC;AAC/D,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,SAASC,GAAGA,CAAA,EAAG;EACX,oBACID,OAAA,CAACd,YAAY;IAAAgB,QAAA,eACTF,OAAA,CAACb,YAAY;MAAAe,QAAA,eACTF,OAAA,CAACjB,MAAM;QAAAmB,QAAA,eACHF,OAAA;UAAKG,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAChBF,OAAA,CAACZ,MAAM;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACVP,OAAA;YAAAE,QAAA,eACIF,OAAA,CAAChB,MAAM;cAAAkB,QAAA,gBACHF,OAAA,CAACf,KAAK;gBAACuB,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAET,OAAA,CAACV,IAAI;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrCP,OAAA,CAACf,KAAK;gBAACuB,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAET,OAAA,CAACT,KAAK;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CP,OAAA,CAACf,KAAK;gBAACuB,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAET,OAAA,CAACR,cAAc;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvDP,OAAA,CAACf,KAAK;gBAACuB,IAAI,EAAC,cAAc;gBAACC,OAAO,eAAET,OAAA,CAACP,aAAa;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzDP,OAAA,CAACf,KAAK;gBAACuB,IAAI,EAAC,eAAe;gBAACC,OAAO,eAAET,OAAA,CAACP,aAAa;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1DP,OAAA,CAACf,KAAK;gBAACuB,IAAI,EAAC,OAAO;gBAACC,OAAO,eAAET,OAAA,CAACN,IAAI;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCP,OAAA,CAACf,KAAK;gBAACuB,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAET,OAAA,CAACL,OAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/CP,OAAA,CAACf,KAAK;gBAACuB,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAET,OAAA,CAACJ,KAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CP,OAAA,CAACf,KAAK;gBAACuB,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAET,OAAA,CAACH,OAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/CP,OAAA,CAACf,KAAK;gBAACuB,IAAI,EAAC,OAAO;gBAACC,OAAO,eAAET,OAAA,CAACF,eAAe;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpDP,OAAA,CAACf,KAAK;gBAACuB,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAET,OAAA,CAACU,eAAe;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACPP,OAAA,CAACX,MAAM;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEvB;AAACI,EAAA,GA5BQV,GAAG;AA8BZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}