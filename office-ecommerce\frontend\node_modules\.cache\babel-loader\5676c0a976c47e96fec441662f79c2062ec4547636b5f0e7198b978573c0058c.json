{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\3d\\\\TableConfigurator.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport * as THREE from 'three';\nimport GLBUploader from './GLBUploader';\nimport '../../styles/configurator.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Advanced3DConfigurator = ({\n  onBack,\n  product\n}) => {\n  _s();\n  // 3D Scene refs\n  const mountRef = useRef(null);\n  const sceneRef = useRef(null);\n  const rendererRef = useRef(null);\n  const productRef = useRef(null);\n  const animationIdRef = useRef(null);\n  const cameraRef = useRef(null);\n\n  // Determine product type\n  const getProductType = () => {\n    if (!product) return 'table';\n    const name = product.name.toLowerCase();\n    if (name.includes('chair')) return 'chair';\n    if (name.includes('cabinet') || name.includes('storage')) return 'cabinet';\n    if (name.includes('shelf')) return 'shelf';\n    if (name.includes('workstation')) return 'workstation';\n    return 'table'; // default\n  };\n  const productType = getProductType();\n\n  // State for configuration\n  const [dimensions, setDimensions] = useState({\n    width: productType === 'chair' ? 60 : 280,\n    depth: productType === 'chair' ? 60 : 140,\n    height: productType === 'chair' ? 80 : 75\n  });\n  const [colors, setColors] = useState({\n    primary: '#6B7280',\n    secondary: '#374151',\n    accent: '#FFFFFF'\n  });\n  const [material, setMaterial] = useState(productType === 'chair' ? 'mesh' : 'wood');\n  const [quantity, setQuantity] = useState(1);\n\n  // GLB Upload state\n  const [isCustomModel, setIsCustomModel] = useState(false);\n  const [customModelInfo, setCustomModelInfo] = useState(null);\n  const [uploadError, setUploadError] = useState('');\n\n  // Mobile detection\n  const [isMobile, setIsMobile] = useState(false);\n  useEffect(() => {\n    const checkMobile = () => {\n      const mobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n      setIsMobile(mobile);\n    };\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Enhanced pricing logic\n  const getBasePrice = () => {\n    const basePrices = {\n      table: 500,\n      chair: 200,\n      cabinet: 800,\n      shelf: 300,\n      workstation: 1200\n    };\n    return basePrices[productType] || 500;\n  };\n  const calculatePrice = () => {\n    let price = getBasePrice();\n\n    // Size multiplier\n    const sizeMultiplier = dimensions.width * dimensions.depth * dimensions.height / 100000;\n    price *= Math.max(0.5, Math.min(2.0, sizeMultiplier));\n\n    // Material multiplier\n    const materialMultipliers = {\n      wood: 1.0,\n      metal: 1.2,\n      glass: 1.5,\n      plastic: 0.8,\n      leather: 1.8,\n      mesh: 1.1,\n      fabric: 0.9,\n      vinyl: 1.0\n    };\n    price *= materialMultipliers[material] || 1.0;\n    return price * quantity;\n  };\n  const getCurrentPrice = () => calculatePrice();\n\n  // GLB Upload handlers\n  const handleModelLoad = (model, modelInfo) => {\n    if (model) {\n      setIsCustomModel(true);\n      setCustomModelInfo(modelInfo);\n      setUploadError('');\n    } else {\n      // Model was removed, recreate default product\n      setIsCustomModel(false);\n      setCustomModelInfo(null);\n      if (sceneRef.current) {\n        createProduct(sceneRef.current);\n      }\n    }\n  };\n  const handleUploadError = error => {\n    setUploadError(error);\n    setIsCustomModel(false);\n    setCustomModelInfo(null);\n  };\n\n  // Camera control state - moved outside useEffect for global access\n  const cameraControlsRef = useRef({\n    targetRotationX: 0,\n    targetRotationY: 0,\n    currentRotationX: 0,\n    currentRotationY: 0,\n    targetDistance: 6,\n    currentDistance: 6,\n    isAnimating: false\n  });\n\n  // View control functions\n  const resetView = viewType => {\n    if (!cameraRef.current) return;\n    const controls = cameraControlsRef.current;\n    controls.isAnimating = true;\n    switch (viewType) {\n      case 'front':\n        controls.targetRotationX = 0;\n        controls.targetRotationY = 0;\n        controls.targetDistance = 6;\n        break;\n      case 'side':\n        controls.targetRotationX = 0;\n        controls.targetRotationY = Math.PI / 2;\n        controls.targetDistance = 6;\n        break;\n      case 'top':\n        controls.targetRotationX = Math.PI / 2;\n        controls.targetRotationY = 0;\n        controls.targetDistance = 8;\n        break;\n      case 'iso':\n      default:\n        controls.targetRotationX = 0.3;\n        controls.targetRotationY = 0.8;\n        controls.targetDistance = 6;\n        break;\n    }\n  };\n  const adjustZoom = direction => {\n    if (!cameraRef.current || !cameraControlsRef.current) return;\n    const controls = cameraControlsRef.current;\n    const zoomAmount = direction * 1.5;\n    const newDistance = controls.targetDistance + zoomAmount;\n\n    // Smooth zoom with bounds checking\n    controls.targetDistance = Math.max(2.5, Math.min(15, newDistance));\n\n    // Add slight animation flag for smoother zoom\n    controls.isAnimating = true;\n    setTimeout(() => {\n      if (controls) controls.isAnimating = false;\n    }, 500);\n  };\n\n  // Create 3D product functions\n  const createTableGeometry = group => {\n    // Table top\n    const topGeometry = new THREE.BoxGeometry(dimensions.width / 100, 0.08, dimensions.depth / 100);\n    const topMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: material === 'glass' ? 0.1 : 0.7,\n      metalness: material === 'metal' ? 0.8 : 0.0,\n      transparent: material === 'glass',\n      opacity: material === 'glass' ? 0.8 : 1.0\n    });\n    const tableTop = new THREE.Mesh(topGeometry, topMaterial);\n    tableTop.position.y = dimensions.height / 100 - 0.04;\n    if (!isMobile) {\n      tableTop.castShadow = true;\n      tableTop.receiveShadow = true;\n    }\n    group.add(tableTop);\n\n    // Legs with mobile optimization\n    const legSegments = isMobile ? 8 : 12;\n    const legGeometry = new THREE.CylinderGeometry(0.03, 0.03, dimensions.height / 100 - 0.08, legSegments);\n    const legMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7,\n      metalness: material === 'metal' ? 0.8 : 0.0\n    });\n    const legPositions = [[-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1], [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1], [-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1], [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1]];\n    legPositions.forEach(pos => {\n      const leg = new THREE.Mesh(legGeometry, legMaterial);\n      leg.position.set(pos[0], pos[1], pos[2]);\n      if (!isMobile) {\n        leg.castShadow = true;\n      }\n      group.add(leg);\n    });\n  };\n  const createChairGeometry = group => {\n    // Seat\n    const seatGeometry = new THREE.BoxGeometry(dimensions.width / 100, 0.06, dimensions.depth / 100);\n    const seatMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const seat = new THREE.Mesh(seatGeometry, seatMaterial);\n    seat.position.y = 0.4;\n    if (!isMobile) {\n      seat.castShadow = true;\n    }\n    group.add(seat);\n\n    // Backrest\n    const backGeometry = new THREE.BoxGeometry(dimensions.width / 100, 0.02, dimensions.height / 150);\n    const backMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const backrest = new THREE.Mesh(backGeometry, backMaterial);\n    backrest.position.set(0, 0.6, -dimensions.depth / 200 + 0.01);\n    if (!isMobile) {\n      backrest.castShadow = true;\n    }\n    group.add(backrest);\n\n    // Base with mobile optimization\n    const baseSegments = isMobile ? 5 : 8;\n    const baseGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.04, baseSegments);\n    const baseMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const base = new THREE.Mesh(baseGeometry, baseMaterial);\n    base.position.y = 0.02;\n    if (!isMobile) {\n      base.castShadow = true;\n    }\n    group.add(base);\n  };\n  const createProduct = scene => {\n    const productGroup = new THREE.Group();\n    switch (productType) {\n      case 'table':\n        createTableGeometry(productGroup);\n        break;\n      case 'chair':\n        createChairGeometry(productGroup);\n        break;\n      default:\n        createTableGeometry(productGroup);\n    }\n    productRef.current = productGroup;\n    scene.add(productGroup);\n  };\n\n  // Update product materials and geometry\n  const updateProduct = () => {\n    if (!productRef.current || !sceneRef.current) return;\n\n    // Remove old product\n    sceneRef.current.remove(productRef.current);\n\n    // Create new product with updated configuration\n    createProduct(sceneRef.current);\n  };\n\n  // 3D Scene Setup\n  useEffect(() => {\n    if (!mountRef.current) return;\n\n    // Scene setup\n    const scene = new THREE.Scene();\n    scene.background = new THREE.Color(0xf5f5f5); // Gray background\n    sceneRef.current = scene;\n\n    // Camera setup\n    const camera = new THREE.PerspectiveCamera(75, mountRef.current.clientWidth / mountRef.current.clientHeight, 0.1, 1000);\n    camera.position.set(4, 3, 4);\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // Renderer setup with mobile optimizations\n    const renderer = new THREE.WebGLRenderer({\n      antialias: !isMobile,\n      alpha: true,\n      powerPreference: isMobile ? \"low-power\" : \"high-performance\"\n    });\n    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);\n    renderer.setPixelRatio(Math.min(window.devicePixelRatio, isMobile ? 1.5 : 2));\n    renderer.setClearColor(0xf5f5f5, 1.0);\n    renderer.shadowMap.enabled = !isMobile;\n    if (!isMobile) {\n      renderer.shadowMap.type = THREE.PCFSoftShadowMap;\n    }\n    mountRef.current.appendChild(renderer.domElement);\n    rendererRef.current = renderer;\n\n    // Lighting System with mobile optimizations\n    const ambientLight = new THREE.AmbientLight(0x404040, isMobile ? 0.8 : 0.6);\n    scene.add(ambientLight);\n    const directionalLight = new THREE.DirectionalLight(0xffffff, isMobile ? 0.8 : 1.0);\n    directionalLight.position.set(5, 5, 5);\n    if (!isMobile) {\n      directionalLight.castShadow = true;\n      directionalLight.shadow.mapSize.width = 1024;\n      directionalLight.shadow.mapSize.height = 1024;\n    }\n    scene.add(directionalLight);\n\n    // Mouse interaction variables\n    let isMouseDown = false;\n    let mouseX = 0;\n    let mouseY = 0;\n\n    // Get camera controls from ref\n    const controls = cameraControlsRef.current;\n\n    // Mouse event handlers\n    const onMouseDown = event => {\n      isMouseDown = true;\n      mouseX = event.clientX;\n      mouseY = event.clientY;\n    };\n    const onMouseMove = event => {\n      if (!isMouseDown) return;\n      const deltaX = event.clientX - mouseX;\n      const deltaY = event.clientY - mouseY;\n\n      // Increased sensitivity for better responsiveness\n      controls.targetRotationY += deltaX * 0.015;\n      controls.targetRotationX += deltaY * 0.015;\n      controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));\n      mouseX = event.clientX;\n      mouseY = event.clientY;\n    };\n    const onMouseUp = () => {\n      isMouseDown = false;\n    };\n    const onWheel = event => {\n      event.preventDefault();\n      const zoomSpeed = isMobile ? 0.008 : 0.01;\n      controls.targetDistance += event.deltaY * zoomSpeed;\n      controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));\n    };\n\n    // Enhanced touch event handlers for mobile\n    let lastTouchDistance = 0;\n    let initialTouchDistance = 0;\n    const getTouchDistance = touches => {\n      if (touches.length < 2) return 0;\n      const dx = touches[0].clientX - touches[1].clientX;\n      const dy = touches[0].clientY - touches[1].clientY;\n      return Math.sqrt(dx * dx + dy * dy);\n    };\n    const onTouchStart = event => {\n      if (event.touches.length === 1) {\n        isMouseDown = true;\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      } else if (event.touches.length === 2) {\n        isMouseDown = false;\n        initialTouchDistance = getTouchDistance(event.touches);\n        lastTouchDistance = initialTouchDistance;\n      }\n    };\n    const onTouchMove = event => {\n      event.preventDefault();\n      if (event.touches.length === 1 && isMouseDown) {\n        // Single finger rotation with increased sensitivity\n        const deltaX = event.touches[0].clientX - mouseX;\n        const deltaY = event.touches[0].clientY - mouseY;\n        controls.targetRotationY += deltaX * 0.012;\n        controls.targetRotationX += deltaY * 0.012;\n        controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      } else if (event.touches.length === 2) {\n        // Two finger pinch zoom\n        const currentDistance = getTouchDistance(event.touches);\n        const deltaDistance = currentDistance - lastTouchDistance;\n        if (Math.abs(deltaDistance) > 3) {\n          // Increased threshold for smoother zoom\n          controls.targetDistance -= deltaDistance * 0.015;\n          controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));\n          lastTouchDistance = currentDistance;\n        }\n      }\n    };\n    const onTouchEnd = event => {\n      if (event.touches.length === 0) {\n        isMouseDown = false;\n      } else if (event.touches.length === 1) {\n        isMouseDown = true;\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      }\n    };\n\n    // Add event listeners to both canvas and document for better coverage\n    const canvas = renderer.domElement;\n\n    // Mouse events\n    canvas.addEventListener('mousedown', onMouseDown);\n    document.addEventListener('mousemove', onMouseMove); // Global mouse move for better tracking\n    document.addEventListener('mouseup', onMouseUp); // Global mouse up\n    canvas.addEventListener('wheel', onWheel, {\n      passive: false\n    });\n\n    // Touch events with better gesture handling\n    canvas.addEventListener('touchstart', onTouchStart, {\n      passive: false\n    });\n    canvas.addEventListener('touchmove', onTouchMove, {\n      passive: false\n    });\n    canvas.addEventListener('touchend', onTouchEnd, {\n      passive: false\n    });\n\n    // Prevent context menu on long press\n    canvas.addEventListener('contextmenu', e => e.preventDefault());\n\n    // Prevent default touch behaviors that might interfere\n    canvas.style.touchAction = 'none';\n    canvas.style.userSelect = 'none';\n\n    // Enhanced camera update function with smooth interpolation\n    const updateCamera = () => {\n      const lerpFactor = controls.isAnimating ? 0.15 : 0.08; // Faster when animating to presets\n\n      // Smooth interpolation\n      controls.currentRotationX += (controls.targetRotationX - controls.currentRotationX) * lerpFactor;\n      controls.currentRotationY += (controls.targetRotationY - controls.currentRotationY) * lerpFactor;\n      controls.currentDistance += (controls.targetDistance - controls.currentDistance) * lerpFactor;\n\n      // Calculate spherical coordinates for camera position\n      const x = Math.sin(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;\n      const y = Math.sin(controls.currentRotationX) * controls.currentDistance + 2;\n      const z = Math.cos(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;\n      camera.position.set(x, y, z);\n      camera.lookAt(0, 0, 0);\n\n      // Stop animation flag when close enough to target\n      if (controls.isAnimating) {\n        const rotXDiff = Math.abs(controls.targetRotationX - controls.currentRotationX);\n        const rotYDiff = Math.abs(controls.targetRotationY - controls.currentRotationY);\n        const distDiff = Math.abs(controls.targetDistance - controls.currentDistance);\n        if (rotXDiff < 0.01 && rotYDiff < 0.01 && distDiff < 0.1) {\n          controls.isAnimating = false;\n        }\n      }\n    };\n\n    // Initialize camera controls with default isometric view\n    controls.targetRotationX = 0.3;\n    controls.targetRotationY = 0.8;\n    controls.targetDistance = 6;\n    controls.currentRotationX = 0.3;\n    controls.currentRotationY = 0.8;\n    controls.currentDistance = 6;\n\n    // Create initial product\n    createProduct(scene);\n\n    // Animation loop\n    const animate = () => {\n      animationIdRef.current = requestAnimationFrame(animate);\n      updateCamera();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // Handle resize\n    const handleResize = () => {\n      if (!mountRef.current) return;\n      const width = mountRef.current.clientWidth;\n      const height = mountRef.current.clientHeight;\n      camera.aspect = width / height;\n      camera.updateProjectionMatrix();\n      renderer.setSize(width, height);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => {\n      window.removeEventListener('resize', handleResize);\n\n      // Remove canvas event listeners\n      const canvas = renderer.domElement;\n      canvas.removeEventListener('mousedown', onMouseDown);\n      canvas.removeEventListener('wheel', onWheel);\n      canvas.removeEventListener('touchstart', onTouchStart);\n      canvas.removeEventListener('touchmove', onTouchMove);\n      canvas.removeEventListener('touchend', onTouchEnd);\n      canvas.removeEventListener('contextmenu', e => e.preventDefault());\n\n      // Remove global event listeners\n      document.removeEventListener('mousemove', onMouseMove);\n      document.removeEventListener('mouseup', onMouseUp);\n      if (animationIdRef.current) {\n        cancelAnimationFrame(animationIdRef.current);\n      }\n      if (mountRef.current && renderer.domElement) {\n        mountRef.current.removeChild(renderer.domElement);\n      }\n      renderer.dispose();\n    };\n  }, [isMobile, dimensions, colors, material, productType]);\n\n  // Update product when configuration changes\n  useEffect(() => {\n    if (sceneRef.current && productRef.current) {\n      updateProduct();\n    }\n  }, [dimensions, colors, material]);\n  const handleAddToCart = () => {\n    const configuration = {\n      productType,\n      dimensions,\n      colors,\n      material,\n      quantity,\n      price: getCurrentPrice()\n    };\n    console.log('Adding to cart:', configuration);\n    alert(`Added ${quantity} ${productType}(s) to cart for $${getCurrentPrice().toFixed(2)}`);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"configurator-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"configurator-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"back-btn\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M19 12H5M12 19l-7-7 7-7\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 13\n          }, this), \"Back to Products\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: [\"Advanced \", productType.charAt(0).toUpperCase() + productType.slice(1), \" Configurator\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 593,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"configurator-main\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"configurator-layout-horizontal\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"viewer-panel\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"config-card viewer-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n                      fill: \"#F0B21B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 615,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M2 17L12 22L22 17\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 616,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M2 12L12 17L22 12\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 617,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"3D Preview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Interactive model of your configured \", productType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"model-viewer-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"model-viewer\",\n                  ref: mountRef\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"viewer-controls-new\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"view-presets-horizontal\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"preset-btn-new\",\n                      onClick: () => resetView('front'),\n                      title: \"Front View\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                          x: \"4\",\n                          y: \"4\",\n                          width: \"16\",\n                          height: \"16\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\",\n                          rx: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 635,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"12\",\n                          cy: \"12\",\n                          r: \"1.5\",\n                          fill: \"currentColor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 636,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 634,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Front\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 638,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 629,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"preset-btn-new\",\n                      onClick: () => resetView('side'),\n                      title: \"Side View\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M4 12h16M12 4v16\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 646,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"12\",\n                          cy: \"12\",\n                          r: \"1.5\",\n                          fill: \"currentColor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 647,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 645,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Side\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 649,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 640,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"preset-btn-new\",\n                      onClick: () => resetView('top'),\n                      title: \"Top View\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 3L3 7L12 11L21 7L12 3Z\",\n                          fill: \"currentColor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 657,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M3 16L12 20L21 16\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 658,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 656,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Top\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 660,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 651,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"preset-btn-new\",\n                      onClick: () => resetView('iso'),\n                      title: \"3D View\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 3L3 7L12 11L21 7L12 3Z\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 668,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M3 16L12 20L21 16\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 669,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M3 11L12 15L21 11\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 670,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 667,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"3D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 672,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 662,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 628,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"zoom-controls-horizontal\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"zoom-btn-new\",\n                      onClick: () => adjustZoom(-1),\n                      title: \"Zoom Out\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"18\",\n                        height: \"18\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"11\",\n                          cy: \"11\",\n                          r: \"8\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 682,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M8 11h6\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 683,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M21 21l-4.35-4.35\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 684,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 681,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"zoom-btn-new\",\n                      onClick: () => adjustZoom(1),\n                      title: \"Zoom In\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"18\",\n                        height: \"18\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"11\",\n                          cy: \"11\",\n                          r: \"8\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 693,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M11 8v6M8 11h6\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 694,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M21 21l-4.35-4.35\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 695,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 692,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 687,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"viewer-instructions\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: isMobile ? \"Drag to rotate • Pinch to zoom • Use preset views\" : \"Drag to rotate • Scroll to zoom • Use preset views\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-info-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: (product === null || product === void 0 ? void 0 : product.name) || `Custom ${productType.charAt(0).toUpperCase() + productType.slice(1)}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"product-description\",\n                children: [\"Configure your perfect \", productType, \" with our advanced 3D customization tool. Adjust dimensions, choose materials, and see your changes in real-time.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-features\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"18\",\n                    height: \"18\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 721,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 722,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Real-time 3D visualization\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 724,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 719,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"18\",\n                    height: \"18\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 728,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 729,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 727,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Custom dimensions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"18\",\n                    height: \"18\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 735,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 736,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Premium materials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"18\",\n                    height: \"18\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 742,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 743,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 741,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Professional quality\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 745,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"config-panel\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"config-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M3 17h18M3 7h18M7 3v18M17 3v18\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 758,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M3 7l4-4 4 4M3 17l4 4 4-4M7 3l-4 4 4 4M17 3l4 4-4 4\",\n                      fill: \"#F0B21B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 759,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 757,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Dimensions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 763,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Adjust size to fit your space\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 764,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"dimension-controls-enhanced\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dimension-group-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dimension-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"dimension-label\",\n                      children: \"Width\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 770,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dimension-value-controls\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          width: Math.max(productType === 'chair' ? 50 : 100, dimensions.width - 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 777,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 776,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 772,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"dimension-display\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-value\",\n                          children: dimensions.width\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 781,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-unit\",\n                          children: \"cm\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 782,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 780,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          width: Math.min(productType === 'chair' ? 80 : 400, dimensions.width + 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12 5v14M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 789,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 788,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 784,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 771,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 769,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"range\",\n                    min: productType === 'chair' ? 50 : 100,\n                    max: productType === 'chair' ? 80 : 400,\n                    value: dimensions.width,\n                    onChange: e => setDimensions({\n                      ...dimensions,\n                      width: parseInt(e.target.value)\n                    }),\n                    className: \"dimension-slider-enhanced\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dimension-group-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dimension-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"dimension-label\",\n                      children: \"Depth\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 806,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dimension-value-controls\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          depth: Math.max(productType === 'chair' ? 50 : 60, dimensions.depth - 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 813,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 812,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 808,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"dimension-display\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-value\",\n                          children: dimensions.depth\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 817,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-unit\",\n                          children: \"cm\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 818,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 816,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          depth: Math.min(productType === 'chair' ? 80 : 200, dimensions.depth + 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12 5v14M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 825,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 824,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 820,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 807,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 805,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"range\",\n                    min: productType === 'chair' ? 50 : 60,\n                    max: productType === 'chair' ? 80 : 200,\n                    value: dimensions.depth,\n                    onChange: e => setDimensions({\n                      ...dimensions,\n                      depth: parseInt(e.target.value)\n                    }),\n                    className: \"dimension-slider-enhanced\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dimension-group-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dimension-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"dimension-label\",\n                      children: \"Height\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 842,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dimension-value-controls\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          height: Math.max(productType === 'chair' ? 70 : 60, dimensions.height - 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 849,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 848,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 844,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"dimension-display\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-value\",\n                          children: dimensions.height\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 853,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-unit\",\n                          children: \"cm\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 854,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 852,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          height: Math.min(productType === 'chair' ? 120 : 120, dimensions.height + 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12 5v14M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 861,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 860,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 856,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 843,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"range\",\n                    min: productType === 'chair' ? 70 : 60,\n                    max: productType === 'chair' ? 120 : 120,\n                    value: dimensions.height,\n                    onChange: e => setDimensions({\n                      ...dimensions,\n                      height: parseInt(e.target.value)\n                    }),\n                    className: \"dimension-slider-enhanced\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"config-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 883,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                      points: \"7,10 12,15 17,10\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 884,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                      x1: \"12\",\n                      y1: \"15\",\n                      x2: \"12\",\n                      y2: \"3\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 885,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 882,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 881,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Custom 3D Model\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 889,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Upload your own GLB/GLTF file\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(GLBUploader, {\n                onModelLoad: handleModelLoad,\n                onError: handleUploadError,\n                currentScene: sceneRef.current,\n                productRef: productRef\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 893,\n                columnNumber: 17\n              }, this), uploadError && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"upload-error\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\",\n                    stroke: \"#dc3545\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 902,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"15\",\n                    y1: \"9\",\n                    x2: \"9\",\n                    y2: \"15\",\n                    stroke: \"#dc3545\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 903,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"9\",\n                    y1: \"9\",\n                    x2: \"15\",\n                    y2: \"15\",\n                    stroke: \"#dc3545\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 904,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 901,\n                  columnNumber: 21\n                }, this), uploadError]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 900,\n                columnNumber: 19\n              }, this), customModelInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"custom-model-status\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M9 12l2 2 4-4\",\n                    stroke: \"#28a745\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 912,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"9\",\n                    stroke: \"#28a745\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 913,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 911,\n                  columnNumber: 21\n                }, this), \"Custom model loaded: \", customModelInfo.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 910,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"config-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"10\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 925,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 2a10 10 0 0 0 0 20 10 10 0 0 1 0-20z\",\n                      fill: \"#F0B21B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 926,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 924,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Colors & Materials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: isCustomModel ? 'Custom model materials are preserved' : 'Choose colors and surface materials'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 931,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 929,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 17\n              }, this), isCustomModel && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"custom-model-notice\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\",\n                    stroke: \"#ffc107\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 8v4M12 16h.01\",\n                    stroke: \"#ffc107\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 938,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 936,\n                  columnNumber: 21\n                }, this), \"Color and material controls are disabled when using a custom 3D model. The model's original materials will be preserved.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 935,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"color-material-grid-enhanced\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"color-section-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"section-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      children: \"Primary Color\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 946,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"color-info\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"current-color-name\",\n                        children: colors.primary === '#6B7280' ? 'Slate Gray' : colors.primary === '#374151' ? 'Dark Gray' : colors.primary === '#1F2937' ? 'Charcoal' : colors.primary === '#111827' ? 'Black' : colors.primary === '#F3F4F6' ? 'Light Gray' : colors.primary === '#E5E7EB' ? 'Silver' : colors.primary === '#F0B21B' ? 'Golden Yellow' : colors.primary === '#DC2626' ? 'Red' : colors.primary === '#059669' ? 'Green' : colors.primary === '#2563EB' ? 'Blue' : colors.primary === '#7C3AED' ? 'Purple' : colors.primary === '#EA580C' ? 'Orange' : 'Custom'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 948,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"current-color-preview\",\n                        style: {\n                          backgroundColor: colors.primary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 962,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 947,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 945,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"color-palette-enhanced\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"color-group\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"color-group-label\",\n                        children: \"Neutrals\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 970,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"color-row\",\n                        children: ['#6B7280', '#374151', '#1F2937', '#111827', '#F3F4F6', '#E5E7EB'].map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: `color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`,\n                          style: {\n                            backgroundColor: color\n                          },\n                          onClick: () => setColors({\n                            ...colors,\n                            primary: color\n                          }),\n                          title: color === '#6B7280' ? 'Slate Gray' : color === '#374151' ? 'Dark Gray' : color === '#1F2937' ? 'Charcoal' : color === '#111827' ? 'Black' : color === '#F3F4F6' ? 'Light Gray' : 'Silver'\n                        }, color, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 973,\n                          columnNumber: 29\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 971,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 969,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"color-group\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"color-group-label\",\n                        children: \"Accent Colors\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 988,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"color-row\",\n                        children: ['#F0B21B', '#DC2626', '#059669', '#2563EB', '#7C3AED', '#EA580C'].map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: `color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`,\n                          style: {\n                            backgroundColor: color\n                          },\n                          onClick: () => setColors({\n                            ...colors,\n                            primary: color\n                          }),\n                          title: color === '#F0B21B' ? 'Golden Yellow' : color === '#DC2626' ? 'Red' : color === '#059669' ? 'Green' : color === '#2563EB' ? 'Blue' : color === '#7C3AED' ? 'Purple' : 'Orange'\n                        }, color, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 991,\n                          columnNumber: 29\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 989,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 987,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 968,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 944,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"material-section-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"section-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      children: \"Material & Finish\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1010,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"current-material-name\",\n                      children: material.charAt(0).toUpperCase() + material.slice(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1011,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"material-options-enhanced\",\n                    children: (productType === 'chair' ? [{\n                      name: 'Mesh',\n                      value: 'mesh',\n                      icon: '🕸️',\n                      desc: 'Breathable mesh fabric'\n                    }, {\n                      name: 'Fabric',\n                      value: 'fabric',\n                      icon: '🧵',\n                      desc: 'Soft upholstery fabric'\n                    }, {\n                      name: 'Leather',\n                      value: 'leather',\n                      icon: '🐄',\n                      desc: 'Premium leather finish'\n                    }] : [{\n                      name: 'Wood',\n                      value: 'wood',\n                      icon: '🌳',\n                      desc: 'Natural wood grain'\n                    }, {\n                      name: 'Metal',\n                      value: 'metal',\n                      icon: '⚙️',\n                      desc: 'Brushed metal finish'\n                    }, {\n                      name: 'Glass',\n                      value: 'glass',\n                      icon: '💎',\n                      desc: 'Tempered glass surface'\n                    }]).map(mat => /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `material-option-enhanced ${material === mat.value ? 'active' : ''}`,\n                      onClick: () => setMaterial(mat.value),\n                      title: mat.desc,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"material-icon\",\n                        children: mat.icon\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1031,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"material-info\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"material-name\",\n                          children: mat.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1033,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"material-desc\",\n                          children: mat.desc\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1034,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1032,\n                        columnNumber: 27\n                      }, this)]\n                    }, mat.value, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1025,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1008,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"config-card pricing-card-modern\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-icon-modern\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2M6.4 15a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm11.2 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4z\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1048,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1047,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1046,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Order Summary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1052,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Configure and add to cart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1051,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1045,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"order-content-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-summary-modern\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-info-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"product-icon\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                          x: \"3\",\n                          y: \"4\",\n                          width: \"18\",\n                          height: \"12\",\n                          rx: \"2\",\n                          stroke: \"#F0B21B\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1063,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M7 8h10M7 12h6\",\n                          stroke: \"#F0B21B\",\n                          strokeWidth: \"1.5\",\n                          strokeLinecap: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1064,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1062,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1061,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"product-details\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"product-name\",\n                        children: [\"Custom \", productType.charAt(0).toUpperCase() + productType.slice(1)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1068,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"product-specs\",\n                        children: [dimensions.width, \"\\xD7\", dimensions.depth, \"\\xD7\", dimensions.height, \"cm\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1069,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1067,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1060,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1059,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"quantity-section-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"section-label\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"18\",\n                      height: \"18\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M9 12l2 2 4-4\",\n                        stroke: \"#F0B21B\",\n                        strokeWidth: \"2\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1078,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"9\",\n                        stroke: \"#F0B21B\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1079,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1077,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Quantity\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1081,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1076,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"quantity-controls-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setQuantity(Math.max(1, quantity - 1)),\n                      className: \"quantity-btn-modern\",\n                      disabled: quantity <= 1,\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"16\",\n                        height: \"16\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5 12h14\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\",\n                          strokeLinecap: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1090,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1089,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1084,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"quantity-display-modern\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"quantity-number-modern\",\n                        children: quantity\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1094,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1093,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setQuantity(quantity + 1),\n                      className: \"quantity-btn-modern\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"16\",\n                        height: \"16\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 5v14M5 12h14\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\",\n                          strokeLinecap: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1101,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1100,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1096,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1083,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1075,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-breakdown-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-row-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"price-label-modern\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"16\",\n                        height: \"16\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"12\",\n                          cy: \"12\",\n                          r: \"10\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1112,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 6v6l4 2\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\",\n                          strokeLinecap: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1113,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1111,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Base Price\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1115,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1110,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"price-amount\",\n                      children: [\"$\", getBasePrice().toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1117,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1109,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-row-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"price-label-modern\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"16\",\n                        height: \"16\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1122,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                          points: \"3.27,6.96 12,12.01 20.73,6.96\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1123,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                          x1: \"12\",\n                          y1: \"22.08\",\n                          x2: \"12\",\n                          y2: \"12\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1124,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1121,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Material & Size\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1126,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1120,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"price-amount\",\n                      children: [\"+$\", (getCurrentPrice() / quantity - getBasePrice()).toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1128,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1119,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-divider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1130,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-row-modern total-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"price-label-modern total-label\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"18\",\n                        height: \"18\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 2L2 7l10 5 10-5-10-5z\",\n                          fill: \"#F0B21B\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1134,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M2 17l10 5 10-5\",\n                          stroke: \"#F0B21B\",\n                          strokeWidth: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1135,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M2 12l10 5 10-5\",\n                          stroke: \"#F0B21B\",\n                          strokeWidth: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1136,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1133,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Total (\", quantity, \" item\", quantity > 1 ? 's' : '', \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1138,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1132,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"price-amount-total\",\n                      children: [\"$\", getCurrentPrice().toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1140,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1131,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1108,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"add-to-cart-btn-modern\",\n                  onClick: handleAddToCart,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"btn-content-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"btn-icon-modern\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\",\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1149,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"9\",\n                          cy: \"20\",\n                          r: \"1\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1150,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"20\",\n                          cy: \"20\",\n                          r: \"1\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1151,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1148,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1147,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"btn-text-modern\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"btn-action-modern\",\n                        children: \"Add to Cart\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1155,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"btn-price-modern\",\n                        children: [\"$\", getCurrentPrice().toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1156,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1154,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1146,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1145,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1057,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 606,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 591,\n    columnNumber: 5\n  }, this);\n};\n_s(Advanced3DConfigurator, \"6EftWyCL2/h+fTmzhgRZ8YhvTpY=\");\n_c = Advanced3DConfigurator;\nexport default Advanced3DConfigurator;\nvar _c;\n$RefreshReg$(_c, \"Advanced3DConfigurator\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "THREE", "GLBUploader", "jsxDEV", "_jsxDEV", "Advanced3DConfigurator", "onBack", "product", "_s", "mountRef", "sceneRef", "rendererRef", "productRef", "animationIdRef", "cameraRef", "getProductType", "name", "toLowerCase", "includes", "productType", "dimensions", "setDimensions", "width", "depth", "height", "colors", "setColors", "primary", "secondary", "accent", "material", "setMaterial", "quantity", "setQuantity", "isCustomModel", "setIsCustomModel", "customModelInfo", "setCustomModelInfo", "uploadError", "setUploadError", "isMobile", "setIsMobile", "checkMobile", "mobile", "window", "innerWidth", "test", "navigator", "userAgent", "addEventListener", "removeEventListener", "getBasePrice", "basePrices", "table", "chair", "cabinet", "shelf", "workstation", "calculatePrice", "price", "sizeMultiplier", "Math", "max", "min", "materialMultipliers", "wood", "metal", "glass", "plastic", "leather", "mesh", "fabric", "vinyl", "getCurrentPrice", "handleModelLoad", "model", "modelInfo", "current", "createProduct", "handleUploadError", "error", "cameraControlsRef", "targetRotationX", "targetRotationY", "currentRotationX", "currentRotationY", "targetDistance", "currentDistance", "isAnimating", "resetView", "viewType", "controls", "PI", "adjustZoom", "direction", "zoomAmount", "newDistance", "setTimeout", "createTableGeometry", "group", "topGeometry", "BoxGeometry", "topMaterial", "MeshStandardMaterial", "color", "roughness", "metalness", "transparent", "opacity", "tableTop", "<PERSON><PERSON>", "position", "y", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "add", "legSegments", "legGeometry", "CylinderGeometry", "legMaterial", "legPositions", "for<PERSON>ach", "pos", "leg", "set", "createChairGeometry", "seatGeometry", "seatMaterial", "seat", "backGeometry", "backMaterial", "backrest", "baseSegments", "baseGeometry", "baseMaterial", "base", "scene", "productGroup", "Group", "updateProduct", "remove", "Scene", "background", "Color", "camera", "PerspectiveCamera", "clientWidth", "clientHeight", "lookAt", "renderer", "WebGLRenderer", "antialias", "alpha", "powerPreference", "setSize", "setPixelRatio", "devicePixelRatio", "setClearColor", "shadowMap", "enabled", "type", "PCFSoftShadowMap", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight", "DirectionalLight", "shadow", "mapSize", "isMouseDown", "mouseX", "mouseY", "onMouseDown", "event", "clientX", "clientY", "onMouseMove", "deltaX", "deltaY", "onMouseUp", "onWheel", "preventDefault", "zoomSpeed", "lastTouchDistance", "initialTouchDistance", "getTouchDistance", "touches", "length", "dx", "dy", "sqrt", "onTouchStart", "onTouchMove", "deltaDistance", "abs", "onTouchEnd", "canvas", "document", "passive", "e", "style", "touchAction", "userSelect", "updateCamera", "lerpFactor", "x", "sin", "cos", "z", "rotXDiff", "rotYDiff", "distDiff", "animate", "requestAnimationFrame", "render", "handleResize", "aspect", "updateProjectionMatrix", "cancelAnimationFrame", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "handleAddToCart", "configuration", "console", "log", "alert", "toFixed", "className", "children", "onClick", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "char<PERSON>t", "toUpperCase", "slice", "ref", "title", "rx", "cx", "cy", "r", "value", "onChange", "parseInt", "target", "points", "x1", "y1", "x2", "y2", "onModelLoad", "onError", "currentScene", "backgroundColor", "map", "icon", "desc", "mat", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/3d/TableConfigurator.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport * as THREE from 'three';\nimport GLBUploader from './GLBUploader';\nimport '../../styles/configurator.css';\n\nconst Advanced3DConfigurator = ({ onBack, product }) => {\n  // 3D Scene refs\n  const mountRef = useRef(null);\n  const sceneRef = useRef(null);\n  const rendererRef = useRef(null);\n  const productRef = useRef(null);\n  const animationIdRef = useRef(null);\n  const cameraRef = useRef(null);\n\n  // Determine product type\n  const getProductType = () => {\n    if (!product) return 'table';\n    const name = product.name.toLowerCase();\n    if (name.includes('chair')) return 'chair';\n    if (name.includes('cabinet') || name.includes('storage')) return 'cabinet';\n    if (name.includes('shelf')) return 'shelf';\n    if (name.includes('workstation')) return 'workstation';\n    return 'table'; // default\n  };\n\n  const productType = getProductType();\n\n  // State for configuration\n  const [dimensions, setDimensions] = useState({\n    width: productType === 'chair' ? 60 : 280,\n    depth: productType === 'chair' ? 60 : 140,\n    height: productType === 'chair' ? 80 : 75\n  });\n\n  const [colors, setColors] = useState({\n    primary: '#6B7280',\n    secondary: '#374151',\n    accent: '#FFFFFF'\n  });\n\n  const [material, setMaterial] = useState(productType === 'chair' ? 'mesh' : 'wood');\n  const [quantity, setQuantity] = useState(1);\n\n  // GLB Upload state\n  const [isCustomModel, setIsCustomModel] = useState(false);\n  const [customModelInfo, setCustomModelInfo] = useState(null);\n  const [uploadError, setUploadError] = useState('');\n\n  // Mobile detection\n  const [isMobile, setIsMobile] = useState(false);\n\n  useEffect(() => {\n    const checkMobile = () => {\n      const mobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n      setIsMobile(mobile);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Enhanced pricing logic\n  const getBasePrice = () => {\n    const basePrices = {\n      table: 500,\n      chair: 200,\n      cabinet: 800,\n      shelf: 300,\n      workstation: 1200\n    };\n    return basePrices[productType] || 500;\n  };\n\n  const calculatePrice = () => {\n    let price = getBasePrice();\n\n    // Size multiplier\n    const sizeMultiplier = (dimensions.width * dimensions.depth * dimensions.height) / 100000;\n    price *= Math.max(0.5, Math.min(2.0, sizeMultiplier));\n\n    // Material multiplier\n    const materialMultipliers = {\n      wood: 1.0,\n      metal: 1.2,\n      glass: 1.5,\n      plastic: 0.8,\n      leather: 1.8,\n      mesh: 1.1,\n      fabric: 0.9,\n      vinyl: 1.0\n    };\n    price *= materialMultipliers[material] || 1.0;\n\n    return price * quantity;\n  };\n\n  const getCurrentPrice = () => calculatePrice();\n\n  // GLB Upload handlers\n  const handleModelLoad = (model, modelInfo) => {\n    if (model) {\n      setIsCustomModel(true);\n      setCustomModelInfo(modelInfo);\n      setUploadError('');\n    } else {\n      // Model was removed, recreate default product\n      setIsCustomModel(false);\n      setCustomModelInfo(null);\n      if (sceneRef.current) {\n        createProduct(sceneRef.current);\n      }\n    }\n  };\n\n  const handleUploadError = (error) => {\n    setUploadError(error);\n    setIsCustomModel(false);\n    setCustomModelInfo(null);\n  };\n\n  // Camera control state - moved outside useEffect for global access\n  const cameraControlsRef = useRef({\n    targetRotationX: 0,\n    targetRotationY: 0,\n    currentRotationX: 0,\n    currentRotationY: 0,\n    targetDistance: 6,\n    currentDistance: 6,\n    isAnimating: false\n  });\n\n  // View control functions\n  const resetView = (viewType) => {\n    if (!cameraRef.current) return;\n\n    const controls = cameraControlsRef.current;\n    controls.isAnimating = true;\n\n    switch (viewType) {\n      case 'front':\n        controls.targetRotationX = 0;\n        controls.targetRotationY = 0;\n        controls.targetDistance = 6;\n        break;\n      case 'side':\n        controls.targetRotationX = 0;\n        controls.targetRotationY = Math.PI / 2;\n        controls.targetDistance = 6;\n        break;\n      case 'top':\n        controls.targetRotationX = Math.PI / 2;\n        controls.targetRotationY = 0;\n        controls.targetDistance = 8;\n        break;\n      case 'iso':\n      default:\n        controls.targetRotationX = 0.3;\n        controls.targetRotationY = 0.8;\n        controls.targetDistance = 6;\n        break;\n    }\n  };\n\n  const adjustZoom = (direction) => {\n    if (!cameraRef.current || !cameraControlsRef.current) return;\n\n    const controls = cameraControlsRef.current;\n    const zoomAmount = direction * 1.5;\n    const newDistance = controls.targetDistance + zoomAmount;\n\n    // Smooth zoom with bounds checking\n    controls.targetDistance = Math.max(2.5, Math.min(15, newDistance));\n\n    // Add slight animation flag for smoother zoom\n    controls.isAnimating = true;\n    setTimeout(() => {\n      if (controls) controls.isAnimating = false;\n    }, 500);\n  };\n\n  // Create 3D product functions\n  const createTableGeometry = (group) => {\n    // Table top\n    const topGeometry = new THREE.BoxGeometry(\n      dimensions.width / 100,\n      0.08,\n      dimensions.depth / 100\n    );\n    const topMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: material === 'glass' ? 0.1 : 0.7,\n      metalness: material === 'metal' ? 0.8 : 0.0,\n      transparent: material === 'glass',\n      opacity: material === 'glass' ? 0.8 : 1.0\n    });\n\n    const tableTop = new THREE.Mesh(topGeometry, topMaterial);\n    tableTop.position.y = dimensions.height / 100 - 0.04;\n    if (!isMobile) {\n      tableTop.castShadow = true;\n      tableTop.receiveShadow = true;\n    }\n    group.add(tableTop);\n\n    // Legs with mobile optimization\n    const legSegments = isMobile ? 8 : 12;\n    const legGeometry = new THREE.CylinderGeometry(0.03, 0.03, dimensions.height / 100 - 0.08, legSegments);\n    const legMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7,\n      metalness: material === 'metal' ? 0.8 : 0.0\n    });\n\n    const legPositions = [\n      [-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1],\n      [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1],\n      [-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1],\n      [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1]\n    ];\n\n    legPositions.forEach(pos => {\n      const leg = new THREE.Mesh(legGeometry, legMaterial);\n      leg.position.set(pos[0], pos[1], pos[2]);\n      if (!isMobile) {\n        leg.castShadow = true;\n      }\n      group.add(leg);\n    });\n  };\n\n  const createChairGeometry = (group) => {\n    // Seat\n    const seatGeometry = new THREE.BoxGeometry(\n      dimensions.width / 100,\n      0.06,\n      dimensions.depth / 100\n    );\n    const seatMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const seat = new THREE.Mesh(seatGeometry, seatMaterial);\n    seat.position.y = 0.4;\n    if (!isMobile) {\n      seat.castShadow = true;\n    }\n    group.add(seat);\n\n    // Backrest\n    const backGeometry = new THREE.BoxGeometry(\n      dimensions.width / 100,\n      0.02,\n      dimensions.height / 150\n    );\n    const backMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const backrest = new THREE.Mesh(backGeometry, backMaterial);\n    backrest.position.set(0, 0.6, -dimensions.depth / 200 + 0.01);\n    if (!isMobile) {\n      backrest.castShadow = true;\n    }\n    group.add(backrest);\n\n    // Base with mobile optimization\n    const baseSegments = isMobile ? 5 : 8;\n    const baseGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.04, baseSegments);\n    const baseMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const base = new THREE.Mesh(baseGeometry, baseMaterial);\n    base.position.y = 0.02;\n    if (!isMobile) {\n      base.castShadow = true;\n    }\n    group.add(base);\n  };\n\n  const createProduct = (scene) => {\n    const productGroup = new THREE.Group();\n\n    switch (productType) {\n      case 'table':\n        createTableGeometry(productGroup);\n        break;\n      case 'chair':\n        createChairGeometry(productGroup);\n        break;\n      default:\n        createTableGeometry(productGroup);\n    }\n\n    productRef.current = productGroup;\n    scene.add(productGroup);\n  };\n\n  // Update product materials and geometry\n  const updateProduct = () => {\n    if (!productRef.current || !sceneRef.current) return;\n\n    // Remove old product\n    sceneRef.current.remove(productRef.current);\n\n    // Create new product with updated configuration\n    createProduct(sceneRef.current);\n  };\n\n  // 3D Scene Setup\n  useEffect(() => {\n    if (!mountRef.current) return;\n\n    // Scene setup\n    const scene = new THREE.Scene();\n    scene.background = new THREE.Color(0xf5f5f5); // Gray background\n    sceneRef.current = scene;\n\n    // Camera setup\n    const camera = new THREE.PerspectiveCamera(\n      75,\n      mountRef.current.clientWidth / mountRef.current.clientHeight,\n      0.1,\n      1000\n    );\n    camera.position.set(4, 3, 4);\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // Renderer setup with mobile optimizations\n    const renderer = new THREE.WebGLRenderer({\n      antialias: !isMobile,\n      alpha: true,\n      powerPreference: isMobile ? \"low-power\" : \"high-performance\"\n    });\n    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);\n    renderer.setPixelRatio(Math.min(window.devicePixelRatio, isMobile ? 1.5 : 2));\n    renderer.setClearColor(0xf5f5f5, 1.0);\n    renderer.shadowMap.enabled = !isMobile;\n    if (!isMobile) {\n      renderer.shadowMap.type = THREE.PCFSoftShadowMap;\n    }\n    mountRef.current.appendChild(renderer.domElement);\n    rendererRef.current = renderer;\n\n    // Lighting System with mobile optimizations\n    const ambientLight = new THREE.AmbientLight(0x404040, isMobile ? 0.8 : 0.6);\n    scene.add(ambientLight);\n\n    const directionalLight = new THREE.DirectionalLight(0xffffff, isMobile ? 0.8 : 1.0);\n    directionalLight.position.set(5, 5, 5);\n    if (!isMobile) {\n      directionalLight.castShadow = true;\n      directionalLight.shadow.mapSize.width = 1024;\n      directionalLight.shadow.mapSize.height = 1024;\n    }\n    scene.add(directionalLight);\n\n    // Mouse interaction variables\n    let isMouseDown = false;\n    let mouseX = 0;\n    let mouseY = 0;\n\n    // Get camera controls from ref\n    const controls = cameraControlsRef.current;\n\n    // Mouse event handlers\n    const onMouseDown = (event) => {\n      isMouseDown = true;\n      mouseX = event.clientX;\n      mouseY = event.clientY;\n    };\n\n    const onMouseMove = (event) => {\n      if (!isMouseDown) return;\n      const deltaX = event.clientX - mouseX;\n      const deltaY = event.clientY - mouseY;\n\n      // Increased sensitivity for better responsiveness\n      controls.targetRotationY += deltaX * 0.015;\n      controls.targetRotationX += deltaY * 0.015;\n\n      controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));\n\n      mouseX = event.clientX;\n      mouseY = event.clientY;\n    };\n\n    const onMouseUp = () => {\n      isMouseDown = false;\n    };\n\n    const onWheel = (event) => {\n      event.preventDefault();\n      const zoomSpeed = isMobile ? 0.008 : 0.01;\n      controls.targetDistance += event.deltaY * zoomSpeed;\n      controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));\n    };\n\n    // Enhanced touch event handlers for mobile\n    let lastTouchDistance = 0;\n    let initialTouchDistance = 0;\n\n    const getTouchDistance = (touches) => {\n      if (touches.length < 2) return 0;\n      const dx = touches[0].clientX - touches[1].clientX;\n      const dy = touches[0].clientY - touches[1].clientY;\n      return Math.sqrt(dx * dx + dy * dy);\n    };\n\n    const onTouchStart = (event) => {\n      if (event.touches.length === 1) {\n        isMouseDown = true;\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      } else if (event.touches.length === 2) {\n        isMouseDown = false;\n        initialTouchDistance = getTouchDistance(event.touches);\n        lastTouchDistance = initialTouchDistance;\n      }\n    };\n\n    const onTouchMove = (event) => {\n      event.preventDefault();\n\n      if (event.touches.length === 1 && isMouseDown) {\n        // Single finger rotation with increased sensitivity\n        const deltaX = event.touches[0].clientX - mouseX;\n        const deltaY = event.touches[0].clientY - mouseY;\n\n        controls.targetRotationY += deltaX * 0.012;\n        controls.targetRotationX += deltaY * 0.012;\n\n        controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));\n\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      } else if (event.touches.length === 2) {\n        // Two finger pinch zoom\n        const currentDistance = getTouchDistance(event.touches);\n        const deltaDistance = currentDistance - lastTouchDistance;\n\n        if (Math.abs(deltaDistance) > 3) { // Increased threshold for smoother zoom\n          controls.targetDistance -= deltaDistance * 0.015;\n          controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));\n          lastTouchDistance = currentDistance;\n        }\n      }\n    };\n\n    const onTouchEnd = (event) => {\n      if (event.touches.length === 0) {\n        isMouseDown = false;\n      } else if (event.touches.length === 1) {\n        isMouseDown = true;\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      }\n    };\n\n    // Add event listeners to both canvas and document for better coverage\n    const canvas = renderer.domElement;\n\n    // Mouse events\n    canvas.addEventListener('mousedown', onMouseDown);\n    document.addEventListener('mousemove', onMouseMove); // Global mouse move for better tracking\n    document.addEventListener('mouseup', onMouseUp); // Global mouse up\n    canvas.addEventListener('wheel', onWheel, { passive: false });\n\n    // Touch events with better gesture handling\n    canvas.addEventListener('touchstart', onTouchStart, { passive: false });\n    canvas.addEventListener('touchmove', onTouchMove, { passive: false });\n    canvas.addEventListener('touchend', onTouchEnd, { passive: false });\n\n    // Prevent context menu on long press\n    canvas.addEventListener('contextmenu', (e) => e.preventDefault());\n\n    // Prevent default touch behaviors that might interfere\n    canvas.style.touchAction = 'none';\n    canvas.style.userSelect = 'none';\n\n    // Enhanced camera update function with smooth interpolation\n    const updateCamera = () => {\n      const lerpFactor = controls.isAnimating ? 0.15 : 0.08; // Faster when animating to presets\n\n      // Smooth interpolation\n      controls.currentRotationX += (controls.targetRotationX - controls.currentRotationX) * lerpFactor;\n      controls.currentRotationY += (controls.targetRotationY - controls.currentRotationY) * lerpFactor;\n      controls.currentDistance += (controls.targetDistance - controls.currentDistance) * lerpFactor;\n\n      // Calculate spherical coordinates for camera position\n      const x = Math.sin(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;\n      const y = Math.sin(controls.currentRotationX) * controls.currentDistance + 2;\n      const z = Math.cos(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;\n\n      camera.position.set(x, y, z);\n      camera.lookAt(0, 0, 0);\n\n      // Stop animation flag when close enough to target\n      if (controls.isAnimating) {\n        const rotXDiff = Math.abs(controls.targetRotationX - controls.currentRotationX);\n        const rotYDiff = Math.abs(controls.targetRotationY - controls.currentRotationY);\n        const distDiff = Math.abs(controls.targetDistance - controls.currentDistance);\n\n        if (rotXDiff < 0.01 && rotYDiff < 0.01 && distDiff < 0.1) {\n          controls.isAnimating = false;\n        }\n      }\n    };\n\n    // Initialize camera controls with default isometric view\n    controls.targetRotationX = 0.3;\n    controls.targetRotationY = 0.8;\n    controls.targetDistance = 6;\n    controls.currentRotationX = 0.3;\n    controls.currentRotationY = 0.8;\n    controls.currentDistance = 6;\n\n    // Create initial product\n    createProduct(scene);\n\n    // Animation loop\n    const animate = () => {\n      animationIdRef.current = requestAnimationFrame(animate);\n      updateCamera();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // Handle resize\n    const handleResize = () => {\n      if (!mountRef.current) return;\n      const width = mountRef.current.clientWidth;\n      const height = mountRef.current.clientHeight;\n      camera.aspect = width / height;\n      camera.updateProjectionMatrix();\n      renderer.setSize(width, height);\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n\n      // Remove canvas event listeners\n      const canvas = renderer.domElement;\n      canvas.removeEventListener('mousedown', onMouseDown);\n      canvas.removeEventListener('wheel', onWheel);\n      canvas.removeEventListener('touchstart', onTouchStart);\n      canvas.removeEventListener('touchmove', onTouchMove);\n      canvas.removeEventListener('touchend', onTouchEnd);\n      canvas.removeEventListener('contextmenu', (e) => e.preventDefault());\n\n      // Remove global event listeners\n      document.removeEventListener('mousemove', onMouseMove);\n      document.removeEventListener('mouseup', onMouseUp);\n\n      if (animationIdRef.current) {\n        cancelAnimationFrame(animationIdRef.current);\n      }\n      if (mountRef.current && renderer.domElement) {\n        mountRef.current.removeChild(renderer.domElement);\n      }\n      renderer.dispose();\n    };\n  }, [isMobile, dimensions, colors, material, productType]);\n\n  // Update product when configuration changes\n  useEffect(() => {\n    if (sceneRef.current && productRef.current) {\n      updateProduct();\n    }\n  }, [dimensions, colors, material]);\n\n  const handleAddToCart = () => {\n    const configuration = {\n      productType,\n      dimensions,\n      colors,\n      material,\n      quantity,\n      price: getCurrentPrice()\n    };\n\n    console.log('Adding to cart:', configuration);\n    alert(`Added ${quantity} ${productType}(s) to cart for $${getCurrentPrice().toFixed(2)}`);\n  };\n\n  return (\n    <div className=\"configurator-container\">\n      {/* Header */}\n      <div className=\"configurator-header\">\n        <div className=\"container\">\n          <button onClick={onBack} className=\"back-btn\">\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n              <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n            Back to Products\n          </button>\n          <h1>Advanced {productType.charAt(0).toUpperCase() + productType.slice(1)} Configurator</h1>\n        </div>\n      </div>\n\n      {/* Main Configuration */}\n      <div className=\"configurator-main\">\n        <div className=\"container\">\n          <div className=\"configurator-layout-horizontal\">\n            {/* Left Side - 3D Model Viewer */}\n            <div className=\"viewer-panel\">\n              <div className=\"config-card viewer-card\">\n                <div className=\"card-header\">\n                  <div className=\"card-icon\">\n                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" fill=\"#F0B21B\"/>\n                      <path d=\"M2 17L12 22L22 17\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                      <path d=\"M2 12L12 17L22 12\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                  </div>\n                  <div className=\"card-title\">\n                    <h4>3D Preview</h4>\n                    <p>Interactive model of your configured {productType}</p>\n                  </div>\n                </div>\n                <div className=\"model-viewer-container\">\n                  <div className=\"model-viewer\" ref={mountRef}></div>\n                  <div className=\"viewer-controls-new\">\n                    <div className=\"view-presets-horizontal\">\n                      <button\n                        className=\"preset-btn-new\"\n                        onClick={() => resetView('front')}\n                        title=\"Front View\"\n                      >\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <rect x=\"4\" y=\"4\" width=\"16\" height=\"16\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\" rx=\"2\"/>\n                          <circle cx=\"12\" cy=\"12\" r=\"1.5\" fill=\"currentColor\"/>\n                        </svg>\n                        <span>Front</span>\n                      </button>\n                      <button\n                        className=\"preset-btn-new\"\n                        onClick={() => resetView('side')}\n                        title=\"Side View\"\n                      >\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M4 12h16M12 4v16\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <circle cx=\"12\" cy=\"12\" r=\"1.5\" fill=\"currentColor\"/>\n                        </svg>\n                        <span>Side</span>\n                      </button>\n                      <button\n                        className=\"preset-btn-new\"\n                        onClick={() => resetView('top')}\n                        title=\"Top View\"\n                      >\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M12 3L3 7L12 11L21 7L12 3Z\" fill=\"currentColor\"/>\n                          <path d=\"M3 16L12 20L21 16\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\"/>\n                        </svg>\n                        <span>Top</span>\n                      </button>\n                      <button\n                        className=\"preset-btn-new\"\n                        onClick={() => resetView('iso')}\n                        title=\"3D View\"\n                      >\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M12 3L3 7L12 11L21 7L12 3Z\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\"/>\n                          <path d=\"M3 16L12 20L21 16\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\"/>\n                          <path d=\"M3 11L12 15L21 11\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\"/>\n                        </svg>\n                        <span>3D</span>\n                      </button>\n                    </div>\n                    <div className=\"zoom-controls-horizontal\">\n                      <button\n                        className=\"zoom-btn-new\"\n                        onClick={() => adjustZoom(-1)}\n                        title=\"Zoom Out\"\n                      >\n                        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <path d=\"M8 11h6\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <path d=\"M21 21l-4.35-4.35\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                        </svg>\n                      </button>\n                      <button\n                        className=\"zoom-btn-new\"\n                        onClick={() => adjustZoom(1)}\n                        title=\"Zoom In\"\n                      >\n                        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <path d=\"M11 8v6M8 11h6\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <path d=\"M21 21l-4.35-4.35\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                        </svg>\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"viewer-instructions\">\n                    <span>\n                      {isMobile\n                        ? \"Drag to rotate • Pinch to zoom • Use preset views\"\n                        : \"Drag to rotate • Scroll to zoom • Use preset views\"\n                      }\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Product Info Below 3D Viewer */}\n              <div className=\"product-info-section\">\n                <h3>{product?.name || `Custom ${productType.charAt(0).toUpperCase() + productType.slice(1)}`}</h3>\n                <p className=\"product-description\">\n                  Configure your perfect {productType} with our advanced 3D customization tool.\n                  Adjust dimensions, choose materials, and see your changes in real-time.\n                </p>\n                <div className=\"product-features\">\n                  <div className=\"feature-item\">\n                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                    <span>Real-time 3D visualization</span>\n                  </div>\n                  <div className=\"feature-item\">\n                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                    <span>Custom dimensions</span>\n                  </div>\n                  <div className=\"feature-item\">\n                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                    <span>Premium materials</span>\n                  </div>\n                  <div className=\"feature-item\">\n                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                    <span>Professional quality</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Right Side - Configuration Panel */}\n            <div className=\"config-panel\">\n              {/* Dimensions */}\n              <div className=\"config-card\">\n                <div className=\"card-header\">\n                  <div className=\"card-icon\">\n                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M3 17h18M3 7h18M7 3v18M17 3v18\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                      <path d=\"M3 7l4-4 4 4M3 17l4 4 4-4M7 3l-4 4 4 4M17 3l4 4-4 4\" fill=\"#F0B21B\"/>\n                    </svg>\n                  </div>\n                  <div className=\"card-title\">\n                    <h4>Dimensions</h4>\n                    <p>Adjust size to fit your space</p>\n                  </div>\n                </div>\n                <div className=\"dimension-controls-enhanced\">\n                  <div className=\"dimension-group-enhanced\">\n                    <div className=\"dimension-header\">\n                      <label className=\"dimension-label\">Width</label>\n                      <div className=\"dimension-value-controls\">\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, width: Math.max(productType === 'chair' ? 50 : 100, dimensions.width - 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                        <div className=\"dimension-display\">\n                          <span className=\"dimension-value\">{dimensions.width}</span>\n                          <span className=\"dimension-unit\">cm</span>\n                        </div>\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, width: Math.min(productType === 'chair' ? 80 : 400, dimensions.width + 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M12 5v14M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                    <input\n                      type=\"range\"\n                      min={productType === 'chair' ? 50 : 100}\n                      max={productType === 'chair' ? 80 : 400}\n                      value={dimensions.width}\n                      onChange={(e) => setDimensions({...dimensions, width: parseInt(e.target.value)})}\n                      className=\"dimension-slider-enhanced\"\n                    />\n                  </div>\n\n                  <div className=\"dimension-group-enhanced\">\n                    <div className=\"dimension-header\">\n                      <label className=\"dimension-label\">Depth</label>\n                      <div className=\"dimension-value-controls\">\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, depth: Math.max(productType === 'chair' ? 50 : 60, dimensions.depth - 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                        <div className=\"dimension-display\">\n                          <span className=\"dimension-value\">{dimensions.depth}</span>\n                          <span className=\"dimension-unit\">cm</span>\n                        </div>\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, depth: Math.min(productType === 'chair' ? 80 : 200, dimensions.depth + 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M12 5v14M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                    <input\n                      type=\"range\"\n                      min={productType === 'chair' ? 50 : 60}\n                      max={productType === 'chair' ? 80 : 200}\n                      value={dimensions.depth}\n                      onChange={(e) => setDimensions({...dimensions, depth: parseInt(e.target.value)})}\n                      className=\"dimension-slider-enhanced\"\n                    />\n                  </div>\n\n                  <div className=\"dimension-group-enhanced\">\n                    <div className=\"dimension-header\">\n                      <label className=\"dimension-label\">Height</label>\n                      <div className=\"dimension-value-controls\">\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, height: Math.max(productType === 'chair' ? 70 : 60, dimensions.height - 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                        <div className=\"dimension-display\">\n                          <span className=\"dimension-value\">{dimensions.height}</span>\n                          <span className=\"dimension-unit\">cm</span>\n                        </div>\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, height: Math.min(productType === 'chair' ? 120 : 120, dimensions.height + 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M12 5v14M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                    <input\n                      type=\"range\"\n                      min={productType === 'chair' ? 70 : 60}\n                      max={productType === 'chair' ? 120 : 120}\n                      value={dimensions.height}\n                      onChange={(e) => setDimensions({...dimensions, height: parseInt(e.target.value)})}\n                      className=\"dimension-slider-enhanced\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Custom 3D Model Upload */}\n              <div className=\"config-card\">\n                <div className=\"card-header\">\n                  <div className=\"card-icon\">\n                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                      <polyline points=\"7,10 12,15 17,10\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                      <line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                  </div>\n                  <div className=\"card-title\">\n                    <h4>Custom 3D Model</h4>\n                    <p>Upload your own GLB/GLTF file</p>\n                  </div>\n                </div>\n                <GLBUploader\n                  onModelLoad={handleModelLoad}\n                  onError={handleUploadError}\n                  currentScene={sceneRef.current}\n                  productRef={productRef}\n                />\n                {uploadError && (\n                  <div className=\"upload-error\">\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#dc3545\" strokeWidth=\"2\"/>\n                      <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" stroke=\"#dc3545\" strokeWidth=\"2\"/>\n                      <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" stroke=\"#dc3545\" strokeWidth=\"2\"/>\n                    </svg>\n                    {uploadError}\n                  </div>\n                )}\n                {customModelInfo && (\n                  <div className=\"custom-model-status\">\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M9 12l2 2 4-4\" stroke=\"#28a745\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#28a745\" strokeWidth=\"2\"/>\n                    </svg>\n                    Custom model loaded: {customModelInfo.name}\n                  </div>\n                )}\n              </div>\n\n              {/* Colors and Materials */}\n              <div className=\"config-card\">\n                <div className=\"card-header\">\n                  <div className=\"card-icon\">\n                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                      <path d=\"M12 2a10 10 0 0 0 0 20 10 10 0 0 1 0-20z\" fill=\"#F0B21B\"/>\n                    </svg>\n                  </div>\n                  <div className=\"card-title\">\n                    <h4>Colors & Materials</h4>\n                    <p>{isCustomModel ? 'Custom model materials are preserved' : 'Choose colors and surface materials'}</p>\n                  </div>\n                </div>\n                {isCustomModel && (\n                  <div className=\"custom-model-notice\">\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#ffc107\" strokeWidth=\"2\"/>\n                      <path d=\"M12 8v4M12 16h.01\" stroke=\"#ffc107\" strokeWidth=\"2\"/>\n                    </svg>\n                    Color and material controls are disabled when using a custom 3D model. The model's original materials will be preserved.\n                  </div>\n                )}\n                <div className=\"color-material-grid-enhanced\">\n                  <div className=\"color-section-enhanced\">\n                    <div className=\"section-header\">\n                      <h5>Primary Color</h5>\n                      <div className=\"color-info\">\n                        <span className=\"current-color-name\">\n                          {colors.primary === '#6B7280' ? 'Slate Gray' :\n                           colors.primary === '#374151' ? 'Dark Gray' :\n                           colors.primary === '#1F2937' ? 'Charcoal' :\n                           colors.primary === '#111827' ? 'Black' :\n                           colors.primary === '#F3F4F6' ? 'Light Gray' :\n                           colors.primary === '#E5E7EB' ? 'Silver' :\n                           colors.primary === '#F0B21B' ? 'Golden Yellow' :\n                           colors.primary === '#DC2626' ? 'Red' :\n                           colors.primary === '#059669' ? 'Green' :\n                           colors.primary === '#2563EB' ? 'Blue' :\n                           colors.primary === '#7C3AED' ? 'Purple' :\n                           colors.primary === '#EA580C' ? 'Orange' : 'Custom'}\n                        </span>\n                        <div\n                          className=\"current-color-preview\"\n                          style={{ backgroundColor: colors.primary }}\n                        ></div>\n                      </div>\n                    </div>\n                    <div className=\"color-palette-enhanced\">\n                      <div className=\"color-group\">\n                        <span className=\"color-group-label\">Neutrals</span>\n                        <div className=\"color-row\">\n                          {['#6B7280', '#374151', '#1F2937', '#111827', '#F3F4F6', '#E5E7EB'].map((color) => (\n                            <button\n                              key={color}\n                              className={`color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`}\n                              style={{ backgroundColor: color }}\n                              onClick={() => setColors({...colors, primary: color})}\n                              title={color === '#6B7280' ? 'Slate Gray' :\n                                     color === '#374151' ? 'Dark Gray' :\n                                     color === '#1F2937' ? 'Charcoal' :\n                                     color === '#111827' ? 'Black' :\n                                     color === '#F3F4F6' ? 'Light Gray' : 'Silver'}\n                            />\n                          ))}\n                        </div>\n                      </div>\n                      <div className=\"color-group\">\n                        <span className=\"color-group-label\">Accent Colors</span>\n                        <div className=\"color-row\">\n                          {['#F0B21B', '#DC2626', '#059669', '#2563EB', '#7C3AED', '#EA580C'].map((color) => (\n                            <button\n                              key={color}\n                              className={`color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`}\n                              style={{ backgroundColor: color }}\n                              onClick={() => setColors({...colors, primary: color})}\n                              title={color === '#F0B21B' ? 'Golden Yellow' :\n                                     color === '#DC2626' ? 'Red' :\n                                     color === '#059669' ? 'Green' :\n                                     color === '#2563EB' ? 'Blue' :\n                                     color === '#7C3AED' ? 'Purple' : 'Orange'}\n                            />\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"material-section-enhanced\">\n                    <div className=\"section-header\">\n                      <h5>Material & Finish</h5>\n                      <span className=\"current-material-name\">\n                        {material.charAt(0).toUpperCase() + material.slice(1)}\n                      </span>\n                    </div>\n                    <div className=\"material-options-enhanced\">\n                      {(productType === 'chair' ? [\n                        { name: 'Mesh', value: 'mesh', icon: '🕸️', desc: 'Breathable mesh fabric' },\n                        { name: 'Fabric', value: 'fabric', icon: '🧵', desc: 'Soft upholstery fabric' },\n                        { name: 'Leather', value: 'leather', icon: '🐄', desc: 'Premium leather finish' }\n                      ] : [\n                        { name: 'Wood', value: 'wood', icon: '🌳', desc: 'Natural wood grain' },\n                        { name: 'Metal', value: 'metal', icon: '⚙️', desc: 'Brushed metal finish' },\n                        { name: 'Glass', value: 'glass', icon: '💎', desc: 'Tempered glass surface' }\n                      ]).map((mat) => (\n                        <button\n                          key={mat.value}\n                          className={`material-option-enhanced ${material === mat.value ? 'active' : ''}`}\n                          onClick={() => setMaterial(mat.value)}\n                          title={mat.desc}\n                        >\n                          <div className=\"material-icon\">{mat.icon}</div>\n                          <div className=\"material-info\">\n                            <span className=\"material-name\">{mat.name}</span>\n                            <span className=\"material-desc\">{mat.desc}</span>\n                          </div>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Modern Order Summary */}\n              <div className=\"config-card pricing-card-modern\">\n                <div className=\"card-header-modern\">\n                  <div className=\"card-icon-modern\">\n                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2M6.4 15a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm11.2 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4z\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                  </div>\n                  <div className=\"card-title-modern\">\n                    <h4>Order Summary</h4>\n                    <p>Configure and add to cart</p>\n                  </div>\n                </div>\n\n                <div className=\"order-content-modern\">\n                  {/* Product Summary */}\n                  <div className=\"product-summary-modern\">\n                    <div className=\"product-info-row\">\n                      <div className=\"product-icon\">\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <rect x=\"3\" y=\"4\" width=\"18\" height=\"12\" rx=\"2\" stroke=\"#F0B21B\" strokeWidth=\"1.5\" fill=\"none\"/>\n                          <path d=\"M7 8h10M7 12h6\" stroke=\"#F0B21B\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                        </svg>\n                      </div>\n                      <div className=\"product-details\">\n                        <span className=\"product-name\">Custom {productType.charAt(0).toUpperCase() + productType.slice(1)}</span>\n                        <span className=\"product-specs\">{dimensions.width}×{dimensions.depth}×{dimensions.height}cm</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Quantity Controls */}\n                  <div className=\"quantity-section-modern\">\n                    <div className=\"section-label\">\n                      <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\n                      </svg>\n                      <span>Quantity</span>\n                    </div>\n                    <div className=\"quantity-controls-modern\">\n                      <button\n                        onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                        className=\"quantity-btn-modern\"\n                        disabled={quantity <= 1}\n                      >\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        </svg>\n                      </button>\n                      <div className=\"quantity-display-modern\">\n                        <span className=\"quantity-number-modern\">{quantity}</span>\n                      </div>\n                      <button\n                        onClick={() => setQuantity(quantity + 1)}\n                        className=\"quantity-btn-modern\"\n                      >\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M12 5v14M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        </svg>\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Price Breakdown */}\n                  <div className=\"price-breakdown-modern\">\n                    <div className=\"price-row-modern\">\n                      <div className=\"price-label-modern\">\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#64748b\" strokeWidth=\"1.5\"/>\n                          <path d=\"M12 6v6l4 2\" stroke=\"#64748b\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                        </svg>\n                        <span>Base Price</span>\n                      </div>\n                      <span className=\"price-amount\">${getBasePrice().toFixed(2)}</span>\n                    </div>\n                    <div className=\"price-row-modern\">\n                      <div className=\"price-label-modern\">\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\" stroke=\"#64748b\" strokeWidth=\"1.5\"/>\n                          <polyline points=\"3.27,6.96 12,12.01 20.73,6.96\" stroke=\"#64748b\" strokeWidth=\"1.5\"/>\n                          <line x1=\"12\" y1=\"22.08\" x2=\"12\" y2=\"12\" stroke=\"#64748b\" strokeWidth=\"1.5\"/>\n                        </svg>\n                        <span>Material & Size</span>\n                      </div>\n                      <span className=\"price-amount\">+${(getCurrentPrice() / quantity - getBasePrice()).toFixed(2)}</span>\n                    </div>\n                    <div className=\"price-divider\"></div>\n                    <div className=\"price-row-modern total-row\">\n                      <div className=\"price-label-modern total-label\">\n                        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M12 2L2 7l10 5 10-5-10-5z\" fill=\"#F0B21B\"/>\n                          <path d=\"M2 17l10 5 10-5\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                          <path d=\"M2 12l10 5 10-5\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                        </svg>\n                        <span>Total ({quantity} item{quantity > 1 ? 's' : ''})</span>\n                      </div>\n                      <span className=\"price-amount-total\">${getCurrentPrice().toFixed(2)}</span>\n                    </div>\n                  </div>\n\n                  {/* Add to Cart Button */}\n                  <button className=\"add-to-cart-btn-modern\" onClick={handleAddToCart}>\n                    <div className=\"btn-content-modern\">\n                      <div className=\"btn-icon-modern\">\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"9\" cy=\"20\" r=\"1\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        <circle cx=\"20\" cy=\"20\" r=\"1\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        </svg>\n                      </div>\n                      <div className=\"btn-text-modern\">\n                        <span className=\"btn-action-modern\">Add to Cart</span>\n                        <span className=\"btn-price-modern\">${getCurrentPrice().toFixed(2)}</span>\n                      </div>\n                    </div>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Advanced3DConfigurator;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtD;EACA,MAAMC,QAAQ,GAAGV,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMW,QAAQ,GAAGX,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMY,WAAW,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMa,UAAU,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMc,cAAc,GAAGd,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMe,SAAS,GAAGf,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMgB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACR,OAAO,EAAE,OAAO,OAAO;IAC5B,MAAMS,IAAI,GAAGT,OAAO,CAACS,IAAI,CAACC,WAAW,CAAC,CAAC;IACvC,IAAID,IAAI,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,OAAO;IAC1C,IAAIF,IAAI,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,IAAI,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,SAAS;IAC1E,IAAIF,IAAI,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,OAAO;IAC1C,IAAIF,IAAI,CAACE,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,aAAa;IACtD,OAAO,OAAO,CAAC,CAAC;EAClB,CAAC;EAED,MAAMC,WAAW,GAAGJ,cAAc,CAAC,CAAC;;EAEpC;EACA,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC;IAC3CwB,KAAK,EAAEH,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG;IACzCI,KAAK,EAAEJ,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG;IACzCK,MAAM,EAAEL,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG;EACzC,CAAC,CAAC;EAEF,MAAM,CAACM,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC;IACnC6B,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAACqB,WAAW,KAAK,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC;EACnF,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;;EAE3C;EACA,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAE/CE,SAAS,CAAC,MAAM;IACd,MAAM0C,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMC,MAAM,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG,IAAI,gEAAgE,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;MACrIP,WAAW,CAACE,MAAM,CAAC;IACrB,CAAC;IAEDD,WAAW,CAAC,CAAC;IACbE,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAEP,WAAW,CAAC;IAC9C,OAAO,MAAME,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAER,WAAW,CAAC;EAChE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG;MACjBC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,GAAG;MACVC,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE,GAAG;MACVC,WAAW,EAAE;IACf,CAAC;IACD,OAAOL,UAAU,CAACjC,WAAW,CAAC,IAAI,GAAG;EACvC,CAAC;EAED,MAAMuC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIC,KAAK,GAAGR,YAAY,CAAC,CAAC;;IAE1B;IACA,MAAMS,cAAc,GAAIxC,UAAU,CAACE,KAAK,GAAGF,UAAU,CAACG,KAAK,GAAGH,UAAU,CAACI,MAAM,GAAI,MAAM;IACzFmC,KAAK,IAAIE,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEH,cAAc,CAAC,CAAC;;IAErD;IACA,MAAMI,mBAAmB,GAAG;MAC1BC,IAAI,EAAE,GAAG;MACTC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,GAAG;MACVC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE,GAAG;MACXC,KAAK,EAAE;IACT,CAAC;IACDb,KAAK,IAAIK,mBAAmB,CAAClC,QAAQ,CAAC,IAAI,GAAG;IAE7C,OAAO6B,KAAK,GAAG3B,QAAQ;EACzB,CAAC;EAED,MAAMyC,eAAe,GAAGA,CAAA,KAAMf,cAAc,CAAC,CAAC;;EAE9C;EACA,MAAMgB,eAAe,GAAGA,CAACC,KAAK,EAAEC,SAAS,KAAK;IAC5C,IAAID,KAAK,EAAE;MACTxC,gBAAgB,CAAC,IAAI,CAAC;MACtBE,kBAAkB,CAACuC,SAAS,CAAC;MAC7BrC,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,MAAM;MACL;MACAJ,gBAAgB,CAAC,KAAK,CAAC;MACvBE,kBAAkB,CAAC,IAAI,CAAC;MACxB,IAAI3B,QAAQ,CAACmE,OAAO,EAAE;QACpBC,aAAa,CAACpE,QAAQ,CAACmE,OAAO,CAAC;MACjC;IACF;EACF,CAAC;EAED,MAAME,iBAAiB,GAAIC,KAAK,IAAK;IACnCzC,cAAc,CAACyC,KAAK,CAAC;IACrB7C,gBAAgB,CAAC,KAAK,CAAC;IACvBE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM4C,iBAAiB,GAAGlF,MAAM,CAAC;IAC/BmF,eAAe,EAAE,CAAC;IAClBC,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,gBAAgB,EAAE,CAAC;IACnBC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAMC,SAAS,GAAIC,QAAQ,IAAK;IAC9B,IAAI,CAAC5E,SAAS,CAAC+D,OAAO,EAAE;IAExB,MAAMc,QAAQ,GAAGV,iBAAiB,CAACJ,OAAO;IAC1Cc,QAAQ,CAACH,WAAW,GAAG,IAAI;IAE3B,QAAQE,QAAQ;MACd,KAAK,OAAO;QACVC,QAAQ,CAACT,eAAe,GAAG,CAAC;QAC5BS,QAAQ,CAACR,eAAe,GAAG,CAAC;QAC5BQ,QAAQ,CAACL,cAAc,GAAG,CAAC;QAC3B;MACF,KAAK,MAAM;QACTK,QAAQ,CAACT,eAAe,GAAG,CAAC;QAC5BS,QAAQ,CAACR,eAAe,GAAGtB,IAAI,CAAC+B,EAAE,GAAG,CAAC;QACtCD,QAAQ,CAACL,cAAc,GAAG,CAAC;QAC3B;MACF,KAAK,KAAK;QACRK,QAAQ,CAACT,eAAe,GAAGrB,IAAI,CAAC+B,EAAE,GAAG,CAAC;QACtCD,QAAQ,CAACR,eAAe,GAAG,CAAC;QAC5BQ,QAAQ,CAACL,cAAc,GAAG,CAAC;QAC3B;MACF,KAAK,KAAK;MACV;QACEK,QAAQ,CAACT,eAAe,GAAG,GAAG;QAC9BS,QAAQ,CAACR,eAAe,GAAG,GAAG;QAC9BQ,QAAQ,CAACL,cAAc,GAAG,CAAC;QAC3B;IACJ;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,SAAS,IAAK;IAChC,IAAI,CAAChF,SAAS,CAAC+D,OAAO,IAAI,CAACI,iBAAiB,CAACJ,OAAO,EAAE;IAEtD,MAAMc,QAAQ,GAAGV,iBAAiB,CAACJ,OAAO;IAC1C,MAAMkB,UAAU,GAAGD,SAAS,GAAG,GAAG;IAClC,MAAME,WAAW,GAAGL,QAAQ,CAACL,cAAc,GAAGS,UAAU;;IAExD;IACAJ,QAAQ,CAACL,cAAc,GAAGzB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEiC,WAAW,CAAC,CAAC;;IAElE;IACAL,QAAQ,CAACH,WAAW,GAAG,IAAI;IAC3BS,UAAU,CAAC,MAAM;MACf,IAAIN,QAAQ,EAAEA,QAAQ,CAACH,WAAW,GAAG,KAAK;IAC5C,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMU,mBAAmB,GAAIC,KAAK,IAAK;IACrC;IACA,MAAMC,WAAW,GAAG,IAAInG,KAAK,CAACoG,WAAW,CACvCjF,UAAU,CAACE,KAAK,GAAG,GAAG,EACtB,IAAI,EACJF,UAAU,CAACG,KAAK,GAAG,GACrB,CAAC;IACD,MAAM+E,WAAW,GAAG,IAAIrG,KAAK,CAACsG,oBAAoB,CAAC;MACjDC,KAAK,EAAE/E,MAAM,CAACE,OAAO;MACrB8E,SAAS,EAAE3E,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;MAC3C4E,SAAS,EAAE5E,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;MAC3C6E,WAAW,EAAE7E,QAAQ,KAAK,OAAO;MACjC8E,OAAO,EAAE9E,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG;IACxC,CAAC,CAAC;IAEF,MAAM+E,QAAQ,GAAG,IAAI5G,KAAK,CAAC6G,IAAI,CAACV,WAAW,EAAEE,WAAW,CAAC;IACzDO,QAAQ,CAACE,QAAQ,CAACC,CAAC,GAAG5F,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI;IACpD,IAAI,CAACgB,QAAQ,EAAE;MACbqE,QAAQ,CAACI,UAAU,GAAG,IAAI;MAC1BJ,QAAQ,CAACK,aAAa,GAAG,IAAI;IAC/B;IACAf,KAAK,CAACgB,GAAG,CAACN,QAAQ,CAAC;;IAEnB;IACA,MAAMO,WAAW,GAAG5E,QAAQ,GAAG,CAAC,GAAG,EAAE;IACrC,MAAM6E,WAAW,GAAG,IAAIpH,KAAK,CAACqH,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAElG,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,EAAE4F,WAAW,CAAC;IACvG,MAAMG,WAAW,GAAG,IAAItH,KAAK,CAACsG,oBAAoB,CAAC;MACjDC,KAAK,EAAE/E,MAAM,CAACE,OAAO;MACrB8E,SAAS,EAAE,GAAG;MACdC,SAAS,EAAE5E,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG;IAC1C,CAAC,CAAC;IAEF,MAAM0F,YAAY,GAAG,CACnB,CAAC,CAACpG,UAAU,CAACE,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,EAAE,CAACJ,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,EACpG,CAACH,UAAU,CAACE,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,EAAE,CAACJ,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,EACnG,CAAC,CAACH,UAAU,CAACE,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,EAAEJ,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,EACnG,CAACH,UAAU,CAACE,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,EAAEJ,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,CACnG;IAEDiG,YAAY,CAACC,OAAO,CAACC,GAAG,IAAI;MAC1B,MAAMC,GAAG,GAAG,IAAI1H,KAAK,CAAC6G,IAAI,CAACO,WAAW,EAAEE,WAAW,CAAC;MACpDI,GAAG,CAACZ,QAAQ,CAACa,GAAG,CAACF,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC;MACxC,IAAI,CAAClF,QAAQ,EAAE;QACbmF,GAAG,CAACV,UAAU,GAAG,IAAI;MACvB;MACAd,KAAK,CAACgB,GAAG,CAACQ,GAAG,CAAC;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,mBAAmB,GAAI1B,KAAK,IAAK;IACrC;IACA,MAAM2B,YAAY,GAAG,IAAI7H,KAAK,CAACoG,WAAW,CACxCjF,UAAU,CAACE,KAAK,GAAG,GAAG,EACtB,IAAI,EACJF,UAAU,CAACG,KAAK,GAAG,GACrB,CAAC;IACD,MAAMwG,YAAY,GAAG,IAAI9H,KAAK,CAACsG,oBAAoB,CAAC;MAClDC,KAAK,EAAE/E,MAAM,CAACE,OAAO;MACrB8E,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAMuB,IAAI,GAAG,IAAI/H,KAAK,CAAC6G,IAAI,CAACgB,YAAY,EAAEC,YAAY,CAAC;IACvDC,IAAI,CAACjB,QAAQ,CAACC,CAAC,GAAG,GAAG;IACrB,IAAI,CAACxE,QAAQ,EAAE;MACbwF,IAAI,CAACf,UAAU,GAAG,IAAI;IACxB;IACAd,KAAK,CAACgB,GAAG,CAACa,IAAI,CAAC;;IAEf;IACA,MAAMC,YAAY,GAAG,IAAIhI,KAAK,CAACoG,WAAW,CACxCjF,UAAU,CAACE,KAAK,GAAG,GAAG,EACtB,IAAI,EACJF,UAAU,CAACI,MAAM,GAAG,GACtB,CAAC;IACD,MAAM0G,YAAY,GAAG,IAAIjI,KAAK,CAACsG,oBAAoB,CAAC;MAClDC,KAAK,EAAE/E,MAAM,CAACE,OAAO;MACrB8E,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAM0B,QAAQ,GAAG,IAAIlI,KAAK,CAAC6G,IAAI,CAACmB,YAAY,EAAEC,YAAY,CAAC;IAC3DC,QAAQ,CAACpB,QAAQ,CAACa,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAACxG,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC;IAC7D,IAAI,CAACiB,QAAQ,EAAE;MACb2F,QAAQ,CAAClB,UAAU,GAAG,IAAI;IAC5B;IACAd,KAAK,CAACgB,GAAG,CAACgB,QAAQ,CAAC;;IAEnB;IACA,MAAMC,YAAY,GAAG5F,QAAQ,GAAG,CAAC,GAAG,CAAC;IACrC,MAAM6F,YAAY,GAAG,IAAIpI,KAAK,CAACqH,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAEc,YAAY,CAAC;IAC7E,MAAME,YAAY,GAAG,IAAIrI,KAAK,CAACsG,oBAAoB,CAAC;MAClDC,KAAK,EAAE/E,MAAM,CAACE,OAAO;MACrB8E,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAM8B,IAAI,GAAG,IAAItI,KAAK,CAAC6G,IAAI,CAACuB,YAAY,EAAEC,YAAY,CAAC;IACvDC,IAAI,CAACxB,QAAQ,CAACC,CAAC,GAAG,IAAI;IACtB,IAAI,CAACxE,QAAQ,EAAE;MACb+F,IAAI,CAACtB,UAAU,GAAG,IAAI;IACxB;IACAd,KAAK,CAACgB,GAAG,CAACoB,IAAI,CAAC;EACjB,CAAC;EAED,MAAMzD,aAAa,GAAI0D,KAAK,IAAK;IAC/B,MAAMC,YAAY,GAAG,IAAIxI,KAAK,CAACyI,KAAK,CAAC,CAAC;IAEtC,QAAQvH,WAAW;MACjB,KAAK,OAAO;QACV+E,mBAAmB,CAACuC,YAAY,CAAC;QACjC;MACF,KAAK,OAAO;QACVZ,mBAAmB,CAACY,YAAY,CAAC;QACjC;MACF;QACEvC,mBAAmB,CAACuC,YAAY,CAAC;IACrC;IAEA7H,UAAU,CAACiE,OAAO,GAAG4D,YAAY;IACjCD,KAAK,CAACrB,GAAG,CAACsB,YAAY,CAAC;EACzB,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAAC/H,UAAU,CAACiE,OAAO,IAAI,CAACnE,QAAQ,CAACmE,OAAO,EAAE;;IAE9C;IACAnE,QAAQ,CAACmE,OAAO,CAAC+D,MAAM,CAAChI,UAAU,CAACiE,OAAO,CAAC;;IAE3C;IACAC,aAAa,CAACpE,QAAQ,CAACmE,OAAO,CAAC;EACjC,CAAC;;EAED;EACA7E,SAAS,CAAC,MAAM;IACd,IAAI,CAACS,QAAQ,CAACoE,OAAO,EAAE;;IAEvB;IACA,MAAM2D,KAAK,GAAG,IAAIvI,KAAK,CAAC4I,KAAK,CAAC,CAAC;IAC/BL,KAAK,CAACM,UAAU,GAAG,IAAI7I,KAAK,CAAC8I,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC9CrI,QAAQ,CAACmE,OAAO,GAAG2D,KAAK;;IAExB;IACA,MAAMQ,MAAM,GAAG,IAAI/I,KAAK,CAACgJ,iBAAiB,CACxC,EAAE,EACFxI,QAAQ,CAACoE,OAAO,CAACqE,WAAW,GAAGzI,QAAQ,CAACoE,OAAO,CAACsE,YAAY,EAC5D,GAAG,EACH,IACF,CAAC;IACDH,MAAM,CAACjC,QAAQ,CAACa,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BoB,MAAM,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBtI,SAAS,CAAC+D,OAAO,GAAGmE,MAAM;;IAE1B;IACA,MAAMK,QAAQ,GAAG,IAAIpJ,KAAK,CAACqJ,aAAa,CAAC;MACvCC,SAAS,EAAE,CAAC/G,QAAQ;MACpBgH,KAAK,EAAE,IAAI;MACXC,eAAe,EAAEjH,QAAQ,GAAG,WAAW,GAAG;IAC5C,CAAC,CAAC;IACF6G,QAAQ,CAACK,OAAO,CAACjJ,QAAQ,CAACoE,OAAO,CAACqE,WAAW,EAAEzI,QAAQ,CAACoE,OAAO,CAACsE,YAAY,CAAC;IAC7EE,QAAQ,CAACM,aAAa,CAAC9F,IAAI,CAACE,GAAG,CAACnB,MAAM,CAACgH,gBAAgB,EAAEpH,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;IAC7E6G,QAAQ,CAACQ,aAAa,CAAC,QAAQ,EAAE,GAAG,CAAC;IACrCR,QAAQ,CAACS,SAAS,CAACC,OAAO,GAAG,CAACvH,QAAQ;IACtC,IAAI,CAACA,QAAQ,EAAE;MACb6G,QAAQ,CAACS,SAAS,CAACE,IAAI,GAAG/J,KAAK,CAACgK,gBAAgB;IAClD;IACAxJ,QAAQ,CAACoE,OAAO,CAACqF,WAAW,CAACb,QAAQ,CAACc,UAAU,CAAC;IACjDxJ,WAAW,CAACkE,OAAO,GAAGwE,QAAQ;;IAE9B;IACA,MAAMe,YAAY,GAAG,IAAInK,KAAK,CAACoK,YAAY,CAAC,QAAQ,EAAE7H,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;IAC3EgG,KAAK,CAACrB,GAAG,CAACiD,YAAY,CAAC;IAEvB,MAAME,gBAAgB,GAAG,IAAIrK,KAAK,CAACsK,gBAAgB,CAAC,QAAQ,EAAE/H,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;IACnF8H,gBAAgB,CAACvD,QAAQ,CAACa,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtC,IAAI,CAACpF,QAAQ,EAAE;MACb8H,gBAAgB,CAACrD,UAAU,GAAG,IAAI;MAClCqD,gBAAgB,CAACE,MAAM,CAACC,OAAO,CAACnJ,KAAK,GAAG,IAAI;MAC5CgJ,gBAAgB,CAACE,MAAM,CAACC,OAAO,CAACjJ,MAAM,GAAG,IAAI;IAC/C;IACAgH,KAAK,CAACrB,GAAG,CAACmD,gBAAgB,CAAC;;IAE3B;IACA,IAAII,WAAW,GAAG,KAAK;IACvB,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIC,MAAM,GAAG,CAAC;;IAEd;IACA,MAAMjF,QAAQ,GAAGV,iBAAiB,CAACJ,OAAO;;IAE1C;IACA,MAAMgG,WAAW,GAAIC,KAAK,IAAK;MAC7BJ,WAAW,GAAG,IAAI;MAClBC,MAAM,GAAGG,KAAK,CAACC,OAAO;MACtBH,MAAM,GAAGE,KAAK,CAACE,OAAO;IACxB,CAAC;IAED,MAAMC,WAAW,GAAIH,KAAK,IAAK;MAC7B,IAAI,CAACJ,WAAW,EAAE;MAClB,MAAMQ,MAAM,GAAGJ,KAAK,CAACC,OAAO,GAAGJ,MAAM;MACrC,MAAMQ,MAAM,GAAGL,KAAK,CAACE,OAAO,GAAGJ,MAAM;;MAErC;MACAjF,QAAQ,CAACR,eAAe,IAAI+F,MAAM,GAAG,KAAK;MAC1CvF,QAAQ,CAACT,eAAe,IAAIiG,MAAM,GAAG,KAAK;MAE1CxF,QAAQ,CAACT,eAAe,GAAGrB,IAAI,CAACC,GAAG,CAAC,CAACD,IAAI,CAAC+B,EAAE,GAAG,CAAC,EAAE/B,IAAI,CAACE,GAAG,CAACF,IAAI,CAAC+B,EAAE,GAAG,CAAC,EAAED,QAAQ,CAACT,eAAe,CAAC,CAAC;MAElGyF,MAAM,GAAGG,KAAK,CAACC,OAAO;MACtBH,MAAM,GAAGE,KAAK,CAACE,OAAO;IACxB,CAAC;IAED,MAAMI,SAAS,GAAGA,CAAA,KAAM;MACtBV,WAAW,GAAG,KAAK;IACrB,CAAC;IAED,MAAMW,OAAO,GAAIP,KAAK,IAAK;MACzBA,KAAK,CAACQ,cAAc,CAAC,CAAC;MACtB,MAAMC,SAAS,GAAG/I,QAAQ,GAAG,KAAK,GAAG,IAAI;MACzCmD,QAAQ,CAACL,cAAc,IAAIwF,KAAK,CAACK,MAAM,GAAGI,SAAS;MACnD5F,QAAQ,CAACL,cAAc,GAAGzB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE4B,QAAQ,CAACL,cAAc,CAAC,CAAC;IAChF,CAAC;;IAED;IACA,IAAIkG,iBAAiB,GAAG,CAAC;IACzB,IAAIC,oBAAoB,GAAG,CAAC;IAE5B,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;MACpC,IAAIA,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;MAChC,MAAMC,EAAE,GAAGF,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO,GAAGY,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO;MAClD,MAAMe,EAAE,GAAGH,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO,GAAGW,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO;MAClD,OAAOnH,IAAI,CAACkI,IAAI,CAACF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;IACrC,CAAC;IAED,MAAME,YAAY,GAAIlB,KAAK,IAAK;MAC9B,IAAIA,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC9BlB,WAAW,GAAG,IAAI;QAClBC,MAAM,GAAGG,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO;QACjCH,MAAM,GAAGE,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO;MACnC,CAAC,MAAM,IAAIF,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACrClB,WAAW,GAAG,KAAK;QACnBe,oBAAoB,GAAGC,gBAAgB,CAACZ,KAAK,CAACa,OAAO,CAAC;QACtDH,iBAAiB,GAAGC,oBAAoB;MAC1C;IACF,CAAC;IAED,MAAMQ,WAAW,GAAInB,KAAK,IAAK;MAC7BA,KAAK,CAACQ,cAAc,CAAC,CAAC;MAEtB,IAAIR,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,IAAIlB,WAAW,EAAE;QAC7C;QACA,MAAMQ,MAAM,GAAGJ,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO,GAAGJ,MAAM;QAChD,MAAMQ,MAAM,GAAGL,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO,GAAGJ,MAAM;QAEhDjF,QAAQ,CAACR,eAAe,IAAI+F,MAAM,GAAG,KAAK;QAC1CvF,QAAQ,CAACT,eAAe,IAAIiG,MAAM,GAAG,KAAK;QAE1CxF,QAAQ,CAACT,eAAe,GAAGrB,IAAI,CAACC,GAAG,CAAC,CAACD,IAAI,CAAC+B,EAAE,GAAG,CAAC,EAAE/B,IAAI,CAACE,GAAG,CAACF,IAAI,CAAC+B,EAAE,GAAG,CAAC,EAAED,QAAQ,CAACT,eAAe,CAAC,CAAC;QAElGyF,MAAM,GAAGG,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO;QACjCH,MAAM,GAAGE,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO;MACnC,CAAC,MAAM,IAAIF,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACrC;QACA,MAAMrG,eAAe,GAAGmG,gBAAgB,CAACZ,KAAK,CAACa,OAAO,CAAC;QACvD,MAAMO,aAAa,GAAG3G,eAAe,GAAGiG,iBAAiB;QAEzD,IAAI3H,IAAI,CAACsI,GAAG,CAACD,aAAa,CAAC,GAAG,CAAC,EAAE;UAAE;UACjCvG,QAAQ,CAACL,cAAc,IAAI4G,aAAa,GAAG,KAAK;UAChDvG,QAAQ,CAACL,cAAc,GAAGzB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE4B,QAAQ,CAACL,cAAc,CAAC,CAAC;UAC9EkG,iBAAiB,GAAGjG,eAAe;QACrC;MACF;IACF,CAAC;IAED,MAAM6G,UAAU,GAAItB,KAAK,IAAK;MAC5B,IAAIA,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC9BlB,WAAW,GAAG,KAAK;MACrB,CAAC,MAAM,IAAII,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACrClB,WAAW,GAAG,IAAI;QAClBC,MAAM,GAAGG,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO;QACjCH,MAAM,GAAGE,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO;MACnC;IACF,CAAC;;IAED;IACA,MAAMqB,MAAM,GAAGhD,QAAQ,CAACc,UAAU;;IAElC;IACAkC,MAAM,CAACpJ,gBAAgB,CAAC,WAAW,EAAE4H,WAAW,CAAC;IACjDyB,QAAQ,CAACrJ,gBAAgB,CAAC,WAAW,EAAEgI,WAAW,CAAC,CAAC,CAAC;IACrDqB,QAAQ,CAACrJ,gBAAgB,CAAC,SAAS,EAAEmI,SAAS,CAAC,CAAC,CAAC;IACjDiB,MAAM,CAACpJ,gBAAgB,CAAC,OAAO,EAAEoI,OAAO,EAAE;MAAEkB,OAAO,EAAE;IAAM,CAAC,CAAC;;IAE7D;IACAF,MAAM,CAACpJ,gBAAgB,CAAC,YAAY,EAAE+I,YAAY,EAAE;MAAEO,OAAO,EAAE;IAAM,CAAC,CAAC;IACvEF,MAAM,CAACpJ,gBAAgB,CAAC,WAAW,EAAEgJ,WAAW,EAAE;MAAEM,OAAO,EAAE;IAAM,CAAC,CAAC;IACrEF,MAAM,CAACpJ,gBAAgB,CAAC,UAAU,EAAEmJ,UAAU,EAAE;MAAEG,OAAO,EAAE;IAAM,CAAC,CAAC;;IAEnE;IACAF,MAAM,CAACpJ,gBAAgB,CAAC,aAAa,EAAGuJ,CAAC,IAAKA,CAAC,CAAClB,cAAc,CAAC,CAAC,CAAC;;IAEjE;IACAe,MAAM,CAACI,KAAK,CAACC,WAAW,GAAG,MAAM;IACjCL,MAAM,CAACI,KAAK,CAACE,UAAU,GAAG,MAAM;;IAEhC;IACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,UAAU,GAAGlH,QAAQ,CAACH,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;;MAEvD;MACAG,QAAQ,CAACP,gBAAgB,IAAI,CAACO,QAAQ,CAACT,eAAe,GAAGS,QAAQ,CAACP,gBAAgB,IAAIyH,UAAU;MAChGlH,QAAQ,CAACN,gBAAgB,IAAI,CAACM,QAAQ,CAACR,eAAe,GAAGQ,QAAQ,CAACN,gBAAgB,IAAIwH,UAAU;MAChGlH,QAAQ,CAACJ,eAAe,IAAI,CAACI,QAAQ,CAACL,cAAc,GAAGK,QAAQ,CAACJ,eAAe,IAAIsH,UAAU;;MAE7F;MACA,MAAMC,CAAC,GAAGjJ,IAAI,CAACkJ,GAAG,CAACpH,QAAQ,CAACN,gBAAgB,CAAC,GAAGxB,IAAI,CAACmJ,GAAG,CAACrH,QAAQ,CAACP,gBAAgB,CAAC,GAAGO,QAAQ,CAACJ,eAAe;MAC9G,MAAMyB,CAAC,GAAGnD,IAAI,CAACkJ,GAAG,CAACpH,QAAQ,CAACP,gBAAgB,CAAC,GAAGO,QAAQ,CAACJ,eAAe,GAAG,CAAC;MAC5E,MAAM0H,CAAC,GAAGpJ,IAAI,CAACmJ,GAAG,CAACrH,QAAQ,CAACN,gBAAgB,CAAC,GAAGxB,IAAI,CAACmJ,GAAG,CAACrH,QAAQ,CAACP,gBAAgB,CAAC,GAAGO,QAAQ,CAACJ,eAAe;MAE9GyD,MAAM,CAACjC,QAAQ,CAACa,GAAG,CAACkF,CAAC,EAAE9F,CAAC,EAAEiG,CAAC,CAAC;MAC5BjE,MAAM,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACA,IAAIzD,QAAQ,CAACH,WAAW,EAAE;QACxB,MAAM0H,QAAQ,GAAGrJ,IAAI,CAACsI,GAAG,CAACxG,QAAQ,CAACT,eAAe,GAAGS,QAAQ,CAACP,gBAAgB,CAAC;QAC/E,MAAM+H,QAAQ,GAAGtJ,IAAI,CAACsI,GAAG,CAACxG,QAAQ,CAACR,eAAe,GAAGQ,QAAQ,CAACN,gBAAgB,CAAC;QAC/E,MAAM+H,QAAQ,GAAGvJ,IAAI,CAACsI,GAAG,CAACxG,QAAQ,CAACL,cAAc,GAAGK,QAAQ,CAACJ,eAAe,CAAC;QAE7E,IAAI2H,QAAQ,GAAG,IAAI,IAAIC,QAAQ,GAAG,IAAI,IAAIC,QAAQ,GAAG,GAAG,EAAE;UACxDzH,QAAQ,CAACH,WAAW,GAAG,KAAK;QAC9B;MACF;IACF,CAAC;;IAED;IACAG,QAAQ,CAACT,eAAe,GAAG,GAAG;IAC9BS,QAAQ,CAACR,eAAe,GAAG,GAAG;IAC9BQ,QAAQ,CAACL,cAAc,GAAG,CAAC;IAC3BK,QAAQ,CAACP,gBAAgB,GAAG,GAAG;IAC/BO,QAAQ,CAACN,gBAAgB,GAAG,GAAG;IAC/BM,QAAQ,CAACJ,eAAe,GAAG,CAAC;;IAE5B;IACAT,aAAa,CAAC0D,KAAK,CAAC;;IAEpB;IACA,MAAM6E,OAAO,GAAGA,CAAA,KAAM;MACpBxM,cAAc,CAACgE,OAAO,GAAGyI,qBAAqB,CAACD,OAAO,CAAC;MACvDT,YAAY,CAAC,CAAC;MACdvD,QAAQ,CAACkE,MAAM,CAAC/E,KAAK,EAAEQ,MAAM,CAAC;IAChC,CAAC;IACDqE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAAC/M,QAAQ,CAACoE,OAAO,EAAE;MACvB,MAAMvD,KAAK,GAAGb,QAAQ,CAACoE,OAAO,CAACqE,WAAW;MAC1C,MAAM1H,MAAM,GAAGf,QAAQ,CAACoE,OAAO,CAACsE,YAAY;MAC5CH,MAAM,CAACyE,MAAM,GAAGnM,KAAK,GAAGE,MAAM;MAC9BwH,MAAM,CAAC0E,sBAAsB,CAAC,CAAC;MAC/BrE,QAAQ,CAACK,OAAO,CAACpI,KAAK,EAAEE,MAAM,CAAC;IACjC,CAAC;IAEDoB,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAEuK,YAAY,CAAC;IAE/C,OAAO,MAAM;MACX5K,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAEsK,YAAY,CAAC;;MAElD;MACA,MAAMnB,MAAM,GAAGhD,QAAQ,CAACc,UAAU;MAClCkC,MAAM,CAACnJ,mBAAmB,CAAC,WAAW,EAAE2H,WAAW,CAAC;MACpDwB,MAAM,CAACnJ,mBAAmB,CAAC,OAAO,EAAEmI,OAAO,CAAC;MAC5CgB,MAAM,CAACnJ,mBAAmB,CAAC,YAAY,EAAE8I,YAAY,CAAC;MACtDK,MAAM,CAACnJ,mBAAmB,CAAC,WAAW,EAAE+I,WAAW,CAAC;MACpDI,MAAM,CAACnJ,mBAAmB,CAAC,UAAU,EAAEkJ,UAAU,CAAC;MAClDC,MAAM,CAACnJ,mBAAmB,CAAC,aAAa,EAAGsJ,CAAC,IAAKA,CAAC,CAAClB,cAAc,CAAC,CAAC,CAAC;;MAEpE;MACAgB,QAAQ,CAACpJ,mBAAmB,CAAC,WAAW,EAAE+H,WAAW,CAAC;MACtDqB,QAAQ,CAACpJ,mBAAmB,CAAC,SAAS,EAAEkI,SAAS,CAAC;MAElD,IAAIvK,cAAc,CAACgE,OAAO,EAAE;QAC1B8I,oBAAoB,CAAC9M,cAAc,CAACgE,OAAO,CAAC;MAC9C;MACA,IAAIpE,QAAQ,CAACoE,OAAO,IAAIwE,QAAQ,CAACc,UAAU,EAAE;QAC3C1J,QAAQ,CAACoE,OAAO,CAAC+I,WAAW,CAACvE,QAAQ,CAACc,UAAU,CAAC;MACnD;MACAd,QAAQ,CAACwE,OAAO,CAAC,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,CAACrL,QAAQ,EAAEpB,UAAU,EAAEK,MAAM,EAAEK,QAAQ,EAAEX,WAAW,CAAC,CAAC;;EAEzD;EACAnB,SAAS,CAAC,MAAM;IACd,IAAIU,QAAQ,CAACmE,OAAO,IAAIjE,UAAU,CAACiE,OAAO,EAAE;MAC1C8D,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACvH,UAAU,EAAEK,MAAM,EAAEK,QAAQ,CAAC,CAAC;EAElC,MAAMgM,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,aAAa,GAAG;MACpB5M,WAAW;MACXC,UAAU;MACVK,MAAM;MACNK,QAAQ;MACRE,QAAQ;MACR2B,KAAK,EAAEc,eAAe,CAAC;IACzB,CAAC;IAEDuJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,aAAa,CAAC;IAC7CG,KAAK,CAAC,SAASlM,QAAQ,IAAIb,WAAW,oBAAoBsD,eAAe,CAAC,CAAC,CAAC0J,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAC3F,CAAC;EAED,oBACE/N,OAAA;IAAKgO,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBAErCjO,OAAA;MAAKgO,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClCjO,OAAA;QAAKgO,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjO,OAAA;UAAQkO,OAAO,EAAEhO,MAAO;UAAC8N,SAAS,EAAC,UAAU;UAAAC,QAAA,gBAC3CjO,OAAA;YAAKkB,KAAK,EAAC,IAAI;YAACE,MAAM,EAAC,IAAI;YAAC+M,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAAH,QAAA,eACzDjO,OAAA;cAAMqO,CAAC,EAAC,yBAAyB;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC,oBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7O,OAAA;UAAAiO,QAAA,GAAI,WAAS,EAAClN,WAAW,CAAC+N,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGhO,WAAW,CAACiO,KAAK,CAAC,CAAC,CAAC,EAAC,eAAa;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7O,OAAA;MAAKgO,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCjO,OAAA;QAAKgO,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBjO,OAAA;UAAKgO,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAE7CjO,OAAA;YAAKgO,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BjO,OAAA;cAAKgO,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCjO,OAAA;gBAAKgO,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjO,OAAA;kBAAKgO,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxBjO,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAAC+M,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDjO,OAAA;sBAAMqO,CAAC,EAAC,4BAA4B;sBAACD,IAAI,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACrD7O,OAAA;sBAAMqO,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAC9D7O,OAAA;sBAAMqO,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7O,OAAA;kBAAKgO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjO,OAAA;oBAAAiO,QAAA,EAAI;kBAAU;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnB7O,OAAA;oBAAAiO,QAAA,GAAG,uCAAqC,EAAClN,WAAW;kBAAA;oBAAA2N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7O,OAAA;gBAAKgO,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCjO,OAAA;kBAAKgO,SAAS,EAAC,cAAc;kBAACiB,GAAG,EAAE5O;gBAAS;kBAAAqO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnD7O,OAAA;kBAAKgO,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClCjO,OAAA;oBAAKgO,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtCjO,OAAA;sBACEgO,SAAS,EAAC,gBAAgB;sBAC1BE,OAAO,EAAEA,CAAA,KAAM7I,SAAS,CAAC,OAAO,CAAE;sBAClC6J,KAAK,EAAC,YAAY;sBAAAjB,QAAA,gBAElBjO,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAAC+M,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDjO,OAAA;0BAAM0M,CAAC,EAAC,GAAG;0BAAC9F,CAAC,EAAC,GAAG;0BAAC1F,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACkN,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC,MAAM;0BAACe,EAAE,EAAC;wBAAG;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACrG7O,OAAA;0BAAQoP,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,KAAK;0BAAClB,IAAI,EAAC;wBAAc;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC,eACN7O,OAAA;wBAAAiO,QAAA,EAAM;sBAAK;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC,eACT7O,OAAA;sBACEgO,SAAS,EAAC,gBAAgB;sBAC1BE,OAAO,EAAEA,CAAA,KAAM7I,SAAS,CAAC,MAAM,CAAE;sBACjC6J,KAAK,EAAC,WAAW;sBAAAjB,QAAA,gBAEjBjO,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAAC+M,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDjO,OAAA;0BAAMqO,CAAC,EAAC,kBAAkB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACpE7O,OAAA;0BAAQoP,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,KAAK;0BAAClB,IAAI,EAAC;wBAAc;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC,eACN7O,OAAA;wBAAAiO,QAAA,EAAM;sBAAI;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACT7O,OAAA;sBACEgO,SAAS,EAAC,gBAAgB;sBAC1BE,OAAO,EAAEA,CAAA,KAAM7I,SAAS,CAAC,KAAK,CAAE;sBAChC6J,KAAK,EAAC,UAAU;sBAAAjB,QAAA,gBAEhBjO,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAAC+M,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDjO,OAAA;0BAAMqO,CAAC,EAAC,4BAA4B;0BAACD,IAAI,EAAC;wBAAc;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAC1D7O,OAAA;0BAAMqO,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E,CAAC,eACN7O,OAAA;wBAAAiO,QAAA,EAAM;sBAAG;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACT7O,OAAA;sBACEgO,SAAS,EAAC,gBAAgB;sBAC1BE,OAAO,EAAEA,CAAA,KAAM7I,SAAS,CAAC,KAAK,CAAE;sBAChC6J,KAAK,EAAC,SAAS;sBAAAjB,QAAA,gBAEfjO,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAAC+M,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDjO,OAAA;0BAAMqO,CAAC,EAAC,4BAA4B;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAC1F7O,OAAA;0BAAMqO,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACjF7O,OAAA;0BAAMqO,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E,CAAC,eACN7O,OAAA;wBAAAiO,QAAA,EAAM;sBAAE;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACN7O,OAAA;oBAAKgO,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,gBACvCjO,OAAA;sBACEgO,SAAS,EAAC,cAAc;sBACxBE,OAAO,EAAEA,CAAA,KAAMzI,UAAU,CAAC,CAAC,CAAC,CAAE;sBAC9ByJ,KAAK,EAAC,UAAU;sBAAAjB,QAAA,eAEhBjO,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAAC+M,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDjO,OAAA;0BAAQoP,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,GAAG;0BAAChB,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACvE7O,OAAA;0BAAMqO,CAAC,EAAC,SAAS;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAC3D7O,OAAA;0BAAMqO,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACT7O,OAAA;sBACEgO,SAAS,EAAC,cAAc;sBACxBE,OAAO,EAAEA,CAAA,KAAMzI,UAAU,CAAC,CAAC,CAAE;sBAC7ByJ,KAAK,EAAC,SAAS;sBAAAjB,QAAA,eAEfjO,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAAC+M,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDjO,OAAA;0BAAQoP,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,GAAG;0BAAChB,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACvE7O,OAAA;0BAAMqO,CAAC,EAAC,gBAAgB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAClE7O,OAAA;0BAAMqO,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7O,OAAA;kBAAKgO,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,eAClCjO,OAAA;oBAAAiO,QAAA,EACG7L,QAAQ,GACL,mDAAmD,GACnD;kBAAoD;oBAAAsM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEpD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7O,OAAA;cAAKgO,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjO,OAAA;gBAAAiO,QAAA,EAAK,CAAA9N,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAES,IAAI,KAAI,UAAUG,WAAW,CAAC+N,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGhO,WAAW,CAACiO,KAAK,CAAC,CAAC,CAAC;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClG7O,OAAA;gBAAGgO,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,GAAC,yBACV,EAAClN,WAAW,EAAC,mHAEtC;cAAA;gBAAA2N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ7O,OAAA;gBAAKgO,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BjO,OAAA;kBAAKgO,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BjO,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAAC+M,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDjO,OAAA;sBAAMqO,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvG7O,OAAA;sBAAQoP,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACN7O,OAAA;oBAAAiO,QAAA,EAAM;kBAA0B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACN7O,OAAA;kBAAKgO,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BjO,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAAC+M,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDjO,OAAA;sBAAMqO,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvG7O,OAAA;sBAAQoP,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACN7O,OAAA;oBAAAiO,QAAA,EAAM;kBAAiB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACN7O,OAAA;kBAAKgO,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BjO,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAAC+M,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDjO,OAAA;sBAAMqO,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvG7O,OAAA;sBAAQoP,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACN7O,OAAA;oBAAAiO,QAAA,EAAM;kBAAiB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACN7O,OAAA;kBAAKgO,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BjO,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAAC+M,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDjO,OAAA;sBAAMqO,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvG7O,OAAA;sBAAQoP,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACN7O,OAAA;oBAAAiO,QAAA,EAAM;kBAAoB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7O,OAAA;YAAKgO,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAE3BjO,OAAA;cAAKgO,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjO,OAAA;gBAAKgO,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjO,OAAA;kBAAKgO,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxBjO,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAAC+M,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDjO,OAAA;sBAAMqO,CAAC,EAAC,gCAAgC;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACjG7O,OAAA;sBAAMqO,CAAC,EAAC,qDAAqD;sBAACD,IAAI,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7O,OAAA;kBAAKgO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjO,OAAA;oBAAAiO,QAAA,EAAI;kBAAU;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnB7O,OAAA;oBAAAiO,QAAA,EAAG;kBAA6B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7O,OAAA;gBAAKgO,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CjO,OAAA;kBAAKgO,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCjO,OAAA;oBAAKgO,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BjO,OAAA;sBAAOgO,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAK;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChD7O,OAAA;sBAAKgO,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,gBACvCjO,OAAA;wBACEgO,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMjN,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEE,KAAK,EAAEuC,IAAI,CAACC,GAAG,CAAC3C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG,EAAEC,UAAU,CAACE,KAAK,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAA+M,QAAA,eAE1HjO,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAAC+M,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDjO,OAAA;4BAAMqO,CAAC,EAAC,UAAU;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACT7O,OAAA;wBAAKgO,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChCjO,OAAA;0BAAMgO,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,EAAEjN,UAAU,CAACE;wBAAK;0BAAAwN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC3D7O,OAAA;0BAAMgO,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,EAAC;wBAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACN7O,OAAA;wBACEgO,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMjN,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEE,KAAK,EAAEuC,IAAI,CAACE,GAAG,CAAC5C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG,EAAEC,UAAU,CAACE,KAAK,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAA+M,QAAA,eAE1HjO,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAAC+M,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDjO,OAAA;4BAAMqO,CAAC,EAAC,kBAAkB;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN7O,OAAA;oBACE4J,IAAI,EAAC,OAAO;oBACZjG,GAAG,EAAE5C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAI;oBACxC2C,GAAG,EAAE3C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAI;oBACxCwO,KAAK,EAAEvO,UAAU,CAACE,KAAM;oBACxBsO,QAAQ,EAAGpD,CAAC,IAAKnL,aAAa,CAAC;sBAAC,GAAGD,UAAU;sBAAEE,KAAK,EAAEuO,QAAQ,CAACrD,CAAC,CAACsD,MAAM,CAACH,KAAK;oBAAC,CAAC,CAAE;oBACjFvB,SAAS,EAAC;kBAA2B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN7O,OAAA;kBAAKgO,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCjO,OAAA;oBAAKgO,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BjO,OAAA;sBAAOgO,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAK;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChD7O,OAAA;sBAAKgO,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,gBACvCjO,OAAA;wBACEgO,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMjN,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEG,KAAK,EAAEsC,IAAI,CAACC,GAAG,CAAC3C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,EAAE,EAAEC,UAAU,CAACG,KAAK,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAA8M,QAAA,eAEzHjO,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAAC+M,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDjO,OAAA;4BAAMqO,CAAC,EAAC,UAAU;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACT7O,OAAA;wBAAKgO,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChCjO,OAAA;0BAAMgO,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,EAAEjN,UAAU,CAACG;wBAAK;0BAAAuN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC3D7O,OAAA;0BAAMgO,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,EAAC;wBAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACN7O,OAAA;wBACEgO,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMjN,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEG,KAAK,EAAEsC,IAAI,CAACE,GAAG,CAAC5C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG,EAAEC,UAAU,CAACG,KAAK,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAA8M,QAAA,eAE1HjO,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAAC+M,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDjO,OAAA;4BAAMqO,CAAC,EAAC,kBAAkB;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN7O,OAAA;oBACE4J,IAAI,EAAC,OAAO;oBACZjG,GAAG,EAAE5C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,EAAG;oBACvC2C,GAAG,EAAE3C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAI;oBACxCwO,KAAK,EAAEvO,UAAU,CAACG,KAAM;oBACxBqO,QAAQ,EAAGpD,CAAC,IAAKnL,aAAa,CAAC;sBAAC,GAAGD,UAAU;sBAAEG,KAAK,EAAEsO,QAAQ,CAACrD,CAAC,CAACsD,MAAM,CAACH,KAAK;oBAAC,CAAC,CAAE;oBACjFvB,SAAS,EAAC;kBAA2B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN7O,OAAA;kBAAKgO,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCjO,OAAA;oBAAKgO,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BjO,OAAA;sBAAOgO,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAM;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACjD7O,OAAA;sBAAKgO,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,gBACvCjO,OAAA;wBACEgO,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMjN,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEI,MAAM,EAAEqC,IAAI,CAACC,GAAG,CAAC3C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,EAAE,EAAEC,UAAU,CAACI,MAAM,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAA6M,QAAA,eAE3HjO,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAAC+M,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDjO,OAAA;4BAAMqO,CAAC,EAAC,UAAU;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACT7O,OAAA;wBAAKgO,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChCjO,OAAA;0BAAMgO,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,EAAEjN,UAAU,CAACI;wBAAM;0BAAAsN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC5D7O,OAAA;0BAAMgO,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,EAAC;wBAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACN7O,OAAA;wBACEgO,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMjN,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEI,MAAM,EAAEqC,IAAI,CAACE,GAAG,CAAC5C,WAAW,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG,EAAEC,UAAU,CAACI,MAAM,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAA6M,QAAA,eAE7HjO,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAAC+M,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDjO,OAAA;4BAAMqO,CAAC,EAAC,kBAAkB;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN7O,OAAA;oBACE4J,IAAI,EAAC,OAAO;oBACZjG,GAAG,EAAE5C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,EAAG;oBACvC2C,GAAG,EAAE3C,WAAW,KAAK,OAAO,GAAG,GAAG,GAAG,GAAI;oBACzCwO,KAAK,EAAEvO,UAAU,CAACI,MAAO;oBACzBoO,QAAQ,EAAGpD,CAAC,IAAKnL,aAAa,CAAC;sBAAC,GAAGD,UAAU;sBAAEI,MAAM,EAAEqO,QAAQ,CAACrD,CAAC,CAACsD,MAAM,CAACH,KAAK;oBAAC,CAAC,CAAE;oBAClFvB,SAAS,EAAC;kBAA2B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7O,OAAA;cAAKgO,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjO,OAAA;gBAAKgO,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjO,OAAA;kBAAKgO,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxBjO,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAAC+M,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDjO,OAAA;sBAAMqO,CAAC,EAAC,2CAA2C;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACtF7O,OAAA;sBAAU2P,MAAM,EAAC,kBAAkB;sBAACrB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACtE7O,OAAA;sBAAM4P,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,GAAG;sBAACzB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7O,OAAA;kBAAKgO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjO,OAAA;oBAAAiO,QAAA,EAAI;kBAAe;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxB7O,OAAA;oBAAAiO,QAAA,EAAG;kBAA6B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7O,OAAA,CAACF,WAAW;gBACVkQ,WAAW,EAAE1L,eAAgB;gBAC7B2L,OAAO,EAAEtL,iBAAkB;gBAC3BuL,YAAY,EAAE5P,QAAQ,CAACmE,OAAQ;gBAC/BjE,UAAU,EAAEA;cAAW;gBAAAkO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,EACD3M,WAAW,iBACVlC,OAAA;gBAAKgO,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BjO,OAAA;kBAAKkB,KAAK,EAAC,IAAI;kBAACE,MAAM,EAAC,IAAI;kBAAC+M,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAH,QAAA,gBACzDjO,OAAA;oBAAQoP,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAAChB,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACjE7O,OAAA;oBAAM4P,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,IAAI;oBAACzB,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACtE7O,OAAA;oBAAM4P,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACzB,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,EACL3M,WAAW;cAAA;gBAAAwM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CACN,EACA7M,eAAe,iBACdhC,OAAA;gBAAKgO,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClCjO,OAAA;kBAAKkB,KAAK,EAAC,IAAI;kBAACE,MAAM,EAAC,IAAI;kBAAC+M,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAH,QAAA,gBACzDjO,OAAA;oBAAMqO,CAAC,EAAC,eAAe;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACvG7O,OAAA;oBAAQoP,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAAChB,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,yBACe,EAAC7M,eAAe,CAACpB,IAAI;cAAA;gBAAA8N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN7O,OAAA;cAAKgO,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjO,OAAA;gBAAKgO,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjO,OAAA;kBAAKgO,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxBjO,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAAC+M,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDjO,OAAA;sBAAQoP,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,IAAI;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACjE7O,OAAA;sBAAMqO,CAAC,EAAC,0CAA0C;sBAACD,IAAI,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7O,OAAA;kBAAKgO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBjO,OAAA;oBAAAiO,QAAA,EAAI;kBAAkB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3B7O,OAAA;oBAAAiO,QAAA,EAAInM,aAAa,GAAG,sCAAsC,GAAG;kBAAqC;oBAAA4M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL/M,aAAa,iBACZ9B,OAAA;gBAAKgO,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClCjO,OAAA;kBAAKkB,KAAK,EAAC,IAAI;kBAACE,MAAM,EAAC,IAAI;kBAAC+M,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAH,QAAA,gBACzDjO,OAAA;oBAAQoP,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAAChB,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACjE7O,OAAA;oBAAMqO,CAAC,EAAC,mBAAmB;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,4HAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,eACD7O,OAAA;gBAAKgO,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3CjO,OAAA;kBAAKgO,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCjO,OAAA;oBAAKgO,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BjO,OAAA;sBAAAiO,QAAA,EAAI;oBAAa;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtB7O,OAAA;sBAAKgO,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBjO,OAAA;wBAAMgO,SAAS,EAAC,oBAAoB;wBAAAC,QAAA,EACjC5M,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,YAAY,GAC3CF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,WAAW,GAC1CF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,UAAU,GACzCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,OAAO,GACtCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,YAAY,GAC3CF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,QAAQ,GACvCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,eAAe,GAC9CF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,KAAK,GACpCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,OAAO,GACtCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,MAAM,GACrCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,QAAQ,GACvCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,QAAQ,GAAG;sBAAQ;wBAAAmN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,eACP7O,OAAA;wBACEgO,SAAS,EAAC,uBAAuB;wBACjC3B,KAAK,EAAE;0BAAE8D,eAAe,EAAE9O,MAAM,CAACE;wBAAQ;sBAAE;wBAAAmN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN7O,OAAA;oBAAKgO,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCjO,OAAA;sBAAKgO,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BjO,OAAA;wBAAMgO,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnD7O,OAAA;wBAAKgO,SAAS,EAAC,WAAW;wBAAAC,QAAA,EACvB,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACmC,GAAG,CAAEhK,KAAK,iBAC5EpG,OAAA;0BAEEgO,SAAS,EAAE,yBAAyB3M,MAAM,CAACE,OAAO,KAAK6E,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;0BAC/EiG,KAAK,EAAE;4BAAE8D,eAAe,EAAE/J;0BAAM,CAAE;0BAClC8H,OAAO,EAAEA,CAAA,KAAM5M,SAAS,CAAC;4BAAC,GAAGD,MAAM;4BAAEE,OAAO,EAAE6E;0BAAK,CAAC,CAAE;0BACtD8I,KAAK,EAAE9I,KAAK,KAAK,SAAS,GAAG,YAAY,GAClCA,KAAK,KAAK,SAAS,GAAG,WAAW,GACjCA,KAAK,KAAK,SAAS,GAAG,UAAU,GAChCA,KAAK,KAAK,SAAS,GAAG,OAAO,GAC7BA,KAAK,KAAK,SAAS,GAAG,YAAY,GAAG;wBAAS,GARhDA,KAAK;0BAAAsI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OASX,CACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN7O,OAAA;sBAAKgO,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BjO,OAAA;wBAAMgO,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAa;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxD7O,OAAA;wBAAKgO,SAAS,EAAC,WAAW;wBAAAC,QAAA,EACvB,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACmC,GAAG,CAAEhK,KAAK,iBAC5EpG,OAAA;0BAEEgO,SAAS,EAAE,yBAAyB3M,MAAM,CAACE,OAAO,KAAK6E,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;0BAC/EiG,KAAK,EAAE;4BAAE8D,eAAe,EAAE/J;0BAAM,CAAE;0BAClC8H,OAAO,EAAEA,CAAA,KAAM5M,SAAS,CAAC;4BAAC,GAAGD,MAAM;4BAAEE,OAAO,EAAE6E;0BAAK,CAAC,CAAE;0BACtD8I,KAAK,EAAE9I,KAAK,KAAK,SAAS,GAAG,eAAe,GACrCA,KAAK,KAAK,SAAS,GAAG,KAAK,GAC3BA,KAAK,KAAK,SAAS,GAAG,OAAO,GAC7BA,KAAK,KAAK,SAAS,GAAG,MAAM,GAC5BA,KAAK,KAAK,SAAS,GAAG,QAAQ,GAAG;wBAAS,GAR5CA,KAAK;0BAAAsI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OASX,CACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN7O,OAAA;kBAAKgO,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxCjO,OAAA;oBAAKgO,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BjO,OAAA;sBAAAiO,QAAA,EAAI;oBAAiB;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1B7O,OAAA;sBAAMgO,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpCvM,QAAQ,CAACoN,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGrN,QAAQ,CAACsN,KAAK,CAAC,CAAC;oBAAC;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7O,OAAA;oBAAKgO,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACvC,CAAClN,WAAW,KAAK,OAAO,GAAG,CAC1B;sBAAEH,IAAI,EAAE,MAAM;sBAAE2O,KAAK,EAAE,MAAM;sBAAEc,IAAI,EAAE,KAAK;sBAAEC,IAAI,EAAE;oBAAyB,CAAC,EAC5E;sBAAE1P,IAAI,EAAE,QAAQ;sBAAE2O,KAAK,EAAE,QAAQ;sBAAEc,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAyB,CAAC,EAC/E;sBAAE1P,IAAI,EAAE,SAAS;sBAAE2O,KAAK,EAAE,SAAS;sBAAEc,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAyB,CAAC,CAClF,GAAG,CACF;sBAAE1P,IAAI,EAAE,MAAM;sBAAE2O,KAAK,EAAE,MAAM;sBAAEc,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAqB,CAAC,EACvE;sBAAE1P,IAAI,EAAE,OAAO;sBAAE2O,KAAK,EAAE,OAAO;sBAAEc,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAuB,CAAC,EAC3E;sBAAE1P,IAAI,EAAE,OAAO;sBAAE2O,KAAK,EAAE,OAAO;sBAAEc,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAyB,CAAC,CAC9E,EAAEF,GAAG,CAAEG,GAAG,iBACTvQ,OAAA;sBAEEgO,SAAS,EAAE,4BAA4BtM,QAAQ,KAAK6O,GAAG,CAAChB,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;sBAChFrB,OAAO,EAAEA,CAAA,KAAMvM,WAAW,CAAC4O,GAAG,CAAChB,KAAK,CAAE;sBACtCL,KAAK,EAAEqB,GAAG,CAACD,IAAK;sBAAArC,QAAA,gBAEhBjO,OAAA;wBAAKgO,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAEsC,GAAG,CAACF;sBAAI;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/C7O,OAAA;wBAAKgO,SAAS,EAAC,eAAe;wBAAAC,QAAA,gBAC5BjO,OAAA;0BAAMgO,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAEsC,GAAG,CAAC3P;wBAAI;0BAAA8N,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACjD7O,OAAA;0BAAMgO,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAEsC,GAAG,CAACD;wBAAI;0BAAA5B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA,GATD0B,GAAG,CAAChB,KAAK;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAUR,CACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7O,OAAA;cAAKgO,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9CjO,OAAA;gBAAKgO,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCjO,OAAA;kBAAKgO,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/BjO,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAAC+M,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,eACzDjO,OAAA;sBAAMqO,CAAC,EAAC,+KAA+K;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7O,OAAA;kBAAKgO,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCjO,OAAA;oBAAAiO,QAAA,EAAI;kBAAa;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtB7O,OAAA;oBAAAiO,QAAA,EAAG;kBAAyB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN7O,OAAA;gBAAKgO,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBAEnCjO,OAAA;kBAAKgO,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,eACrCjO,OAAA;oBAAKgO,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BjO,OAAA;sBAAKgO,SAAS,EAAC,cAAc;sBAAAC,QAAA,eAC3BjO,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAAC+M,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDjO,OAAA;0BAAM0M,CAAC,EAAC,GAAG;0BAAC9F,CAAC,EAAC,GAAG;0BAAC1F,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAAC+N,EAAE,EAAC,GAAG;0BAACb,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAChG7O,OAAA;0BAAMqO,CAAC,EAAC,gBAAgB;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC,KAAK;0BAACC,aAAa,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN7O,OAAA;sBAAKgO,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBAC9BjO,OAAA;wBAAMgO,SAAS,EAAC,cAAc;wBAAAC,QAAA,GAAC,SAAO,EAAClN,WAAW,CAAC+N,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGhO,WAAW,CAACiO,KAAK,CAAC,CAAC,CAAC;sBAAA;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACzG7O,OAAA;wBAAMgO,SAAS,EAAC,eAAe;wBAAAC,QAAA,GAAEjN,UAAU,CAACE,KAAK,EAAC,MAAC,EAACF,UAAU,CAACG,KAAK,EAAC,MAAC,EAACH,UAAU,CAACI,MAAM,EAAC,IAAE;sBAAA;wBAAAsN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN7O,OAAA;kBAAKgO,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCjO,OAAA;oBAAKgO,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BjO,OAAA;sBAAKkB,KAAK,EAAC,IAAI;sBAACE,MAAM,EAAC,IAAI;sBAAC+M,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAH,QAAA,gBACzDjO,OAAA;wBAAMqO,CAAC,EAAC,eAAe;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC,GAAG;wBAACC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eACvG7O,OAAA;wBAAQoP,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,GAAG;wBAAChB,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC,eACN7O,OAAA;sBAAAiO,QAAA,EAAM;oBAAQ;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACN7O,OAAA;oBAAKgO,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,gBACvCjO,OAAA;sBACEkO,OAAO,EAAEA,CAAA,KAAMrM,WAAW,CAAC4B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE9B,QAAQ,GAAG,CAAC,CAAC,CAAE;sBACtDoM,SAAS,EAAC,qBAAqB;sBAC/BwC,QAAQ,EAAE5O,QAAQ,IAAI,CAAE;sBAAAqM,QAAA,eAExBjO,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAAC+M,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,eACzDjO,OAAA;0BAAMqO,CAAC,EAAC,UAAU;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,GAAG;0BAACC,aAAa,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACT7O,OAAA;sBAAKgO,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,eACtCjO,OAAA;wBAAMgO,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,EAAErM;sBAAQ;wBAAA8M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC,eACN7O,OAAA;sBACEkO,OAAO,EAAEA,CAAA,KAAMrM,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAE;sBACzCoM,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,eAE/BjO,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAAC+M,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,eACzDjO,OAAA;0BAAMqO,CAAC,EAAC,kBAAkB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,GAAG;0BAACC,aAAa,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN7O,OAAA;kBAAKgO,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCjO,OAAA;oBAAKgO,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BjO,OAAA;sBAAKgO,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBACjCjO,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAAC+M,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDjO,OAAA;0BAAQoP,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,IAAI;0BAAChB,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACnE7O,OAAA;0BAAMqO,CAAC,EAAC,aAAa;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC,KAAK;0BAACC,aAAa,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E,CAAC,eACN7O,OAAA;wBAAAiO,QAAA,EAAM;sBAAU;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,eACN7O,OAAA;sBAAMgO,SAAS,EAAC,cAAc;sBAAAC,QAAA,GAAC,GAAC,EAAClL,YAAY,CAAC,CAAC,CAACgL,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACN7O,OAAA;oBAAKgO,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BjO,OAAA;sBAAKgO,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBACjCjO,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAAC+M,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDjO,OAAA;0BAAMqO,CAAC,EAAC,2HAA2H;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACxK7O,OAAA;0BAAU2P,MAAM,EAAC,+BAA+B;0BAACrB,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACrF7O,OAAA;0BAAM4P,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,OAAO;0BAACC,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACzB,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1E,CAAC,eACN7O,OAAA;wBAAAiO,QAAA,EAAM;sBAAe;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACN7O,OAAA;sBAAMgO,SAAS,EAAC,cAAc;sBAAAC,QAAA,GAAC,IAAE,EAAC,CAAC5J,eAAe,CAAC,CAAC,GAAGzC,QAAQ,GAAGmB,YAAY,CAAC,CAAC,EAAEgL,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjG,CAAC,eACN7O,OAAA;oBAAKgO,SAAS,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrC7O,OAAA;oBAAKgO,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBACzCjO,OAAA;sBAAKgO,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBAC7CjO,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAAC+M,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDjO,OAAA;0BAAMqO,CAAC,EAAC,2BAA2B;0BAACD,IAAI,EAAC;wBAAS;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACpD7O,OAAA;0BAAMqO,CAAC,EAAC,iBAAiB;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAG;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAC5D7O,OAAA;0BAAMqO,CAAC,EAAC,iBAAiB;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAG;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD,CAAC,eACN7O,OAAA;wBAAAiO,QAAA,GAAM,SAAO,EAACrM,QAAQ,EAAC,OAAK,EAACA,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,GAAC;sBAAA;wBAAA8M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACN7O,OAAA;sBAAMgO,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,GAAC,GAAC,EAAC5J,eAAe,CAAC,CAAC,CAAC0J,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN7O,OAAA;kBAAQgO,SAAS,EAAC,wBAAwB;kBAACE,OAAO,EAAER,eAAgB;kBAAAO,QAAA,eAClEjO,OAAA;oBAAKgO,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBACjCjO,OAAA;sBAAKgO,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,eAC9BjO,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAAC+M,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDjO,OAAA;0BAAMqO,CAAC,EAAC,uGAAuG;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,GAAG;0BAACC,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC;wBAAO;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACtM7O,OAAA;0BAAQoP,EAAE,EAAC,GAAG;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,GAAG;0BAAChB,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAG;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACpE7O,OAAA;0BAAQoP,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,GAAG;0BAAChB,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAG;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN7O,OAAA;sBAAKgO,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBAC9BjO,OAAA;wBAAMgO,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAW;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACtD7O,OAAA;wBAAMgO,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,GAAC,GAAC,EAAC5J,eAAe,CAAC,CAAC,CAAC0J,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzO,EAAA,CA1oCIH,sBAAsB;AAAAwQ,EAAA,GAAtBxQ,sBAAsB;AA4oC5B,eAAeA,sBAAsB;AAAC,IAAAwQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}