import React, { useState, useRef } from 'react';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import * as THREE from 'three';

const GLBUploader = ({ onModelLoad, onError, currentScene, productRef }) => {
    const [isUploading, setIsUploading] = useState(false);
    const [uploadedModel, setUploadedModel] = useState(null);
    const [modelInfo, setModelInfo] = useState(null);
    const fileInputRef = useRef(null);
    const loaderRef = useRef(new GLTFLoader());

    const handleFileSelect = (event) => {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type
        if (!file.name.toLowerCase().endsWith('.glb') && !file.name.toLowerCase().endsWith('.gltf')) {
            onError?.('Please select a valid GLB or GLTF file');
            return;
        }

        // Validate file size (max 50MB)
        const maxSize = 50 * 1024 * 1024; // 50MB
        if (file.size > maxSize) {
            onError?.('File size too large. Please select a file smaller than 50MB');
            return;
        }

        loadGLBFile(file);
    };

    const loadGLBFile = async (file) => {
        setIsUploading(true);
        
        try {
            // Create URL for the file
            const fileURL = URL.createObjectURL(file);
            
            // Load the GLB/GLTF file
            const gltf = await new Promise((resolve, reject) => {
                loaderRef.current.load(
                    fileURL,
                    (gltf) => resolve(gltf),
                    (progress) => {
                        console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%');
                    },
                    (error) => reject(error)
                );
            });

            // Clean up the URL
            URL.revokeObjectURL(fileURL);

            // Process the loaded model
            const model = gltf.scene;
            
            // Get model information
            const box = new THREE.Box3().setFromObject(model);
            const size = box.getSize(new THREE.Vector3());
            const center = box.getCenter(new THREE.Vector3());

            const modelInfo = {
                name: file.name,
                size: file.size,
                dimensions: {
                    width: size.x.toFixed(2),
                    height: size.y.toFixed(2),
                    depth: size.z.toFixed(2)
                },
                triangles: countTriangles(model),
                materials: countMaterials(model)
            };

            // Center the model
            model.position.sub(center);
            
            // Scale model to reasonable size (optional)
            const maxDimension = Math.max(size.x, size.y, size.z);
            if (maxDimension > 5) {
                const scale = 5 / maxDimension;
                model.scale.setScalar(scale);
            }

            // Enable shadows for all meshes
            model.traverse((child) => {
                if (child.isMesh) {
                    child.castShadow = true;
                    child.receiveShadow = true;
                    
                    // Ensure materials are properly configured
                    if (child.material) {
                        child.material.needsUpdate = true;
                    }
                }
            });

            // Replace current product with uploaded model
            if (currentScene && productRef.current) {
                // Remove existing product
                currentScene.remove(productRef.current);
                
                // Add new model
                currentScene.add(model);
                productRef.current = model;
            }

            setUploadedModel(model);
            setModelInfo(modelInfo);
            onModelLoad?.(model, modelInfo);

        } catch (error) {
            console.error('Error loading GLB file:', error);
            onError?.('Failed to load 3D model. Please check the file format and try again.');
        } finally {
            setIsUploading(false);
        }
    };

    const countTriangles = (object) => {
        let triangles = 0;
        object.traverse((child) => {
            if (child.isMesh && child.geometry) {
                const geometry = child.geometry;
                if (geometry.index) {
                    triangles += geometry.index.count / 3;
                } else {
                    triangles += geometry.attributes.position.count / 3;
                }
            }
        });
        return Math.floor(triangles);
    };

    const countMaterials = (object) => {
        const materials = new Set();
        object.traverse((child) => {
            if (child.isMesh && child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(mat => materials.add(mat.uuid));
                } else {
                    materials.add(child.material.uuid);
                }
            }
        });
        return materials.size;
    };

    const removeUploadedModel = () => {
        if (currentScene && productRef.current && uploadedModel) {
            currentScene.remove(productRef.current);
            setUploadedModel(null);
            setModelInfo(null);
            
            // Notify parent to recreate default product
            onModelLoad?.(null, null);
        }
    };

    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return (
        <div className="glb-uploader">
            <div className="upload-section">
                <h4>Custom 3D Model</h4>
                <p>Upload your own GLB/GLTF file to replace the default model</p>
                
                <div className="upload-controls">
                    <input
                        ref={fileInputRef}
                        type="file"
                        accept=".glb,.gltf"
                        onChange={handleFileSelect}
                        style={{ display: 'none' }}
                    />
                    
                    <button
                        className="upload-btn"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploading}
                    >
                        {isUploading ? (
                            <>
                                <div className="spinner"></div>
                                Uploading...
                            </>
                        ) : (
                            <>
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" strokeWidth="2"/>
                                    <polyline points="7,10 12,15 17,10" stroke="currentColor" strokeWidth="2"/>
                                    <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" strokeWidth="2"/>
                                </svg>
                                Upload GLB/GLTF
                            </>
                        )}
                    </button>

                    {uploadedModel && (
                        <button
                            className="remove-btn"
                            onClick={removeUploadedModel}
                            title="Remove uploaded model"
                        >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2"/>
                            </svg>
                            Remove
                        </button>
                    )}
                </div>

                {modelInfo && (
                    <div className="model-info">
                        <h5>Model Information</h5>
                        <div className="info-grid">
                            <div className="info-item">
                                <span className="label">File:</span>
                                <span className="value">{modelInfo.name}</span>
                            </div>
                            <div className="info-item">
                                <span className="label">Size:</span>
                                <span className="value">{formatFileSize(modelInfo.size)}</span>
                            </div>
                            <div className="info-item">
                                <span className="label">Dimensions:</span>
                                <span className="value">
                                    {modelInfo.dimensions.width} × {modelInfo.dimensions.height} × {modelInfo.dimensions.depth}
                                </span>
                            </div>
                            <div className="info-item">
                                <span className="label">Triangles:</span>
                                <span className="value">{modelInfo.triangles.toLocaleString()}</span>
                            </div>
                            <div className="info-item">
                                <span className="label">Materials:</span>
                                <span className="value">{modelInfo.materials}</span>
                            </div>
                        </div>
                    </div>
                )}

                <div className="upload-tips">
                    <h5>Tips for best results:</h5>
                    <ul>
                        <li>Use GLB format for better compression</li>
                        <li>Keep file size under 50MB</li>
                        <li>Optimize textures for web use</li>
                        <li>Center your model at origin (0,0,0)</li>
                    </ul>
                </div>
            </div>
        </div>
    );
};

export default GLBUploader;
