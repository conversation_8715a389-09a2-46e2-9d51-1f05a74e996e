{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\3d\\\\GLBUploader.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport * as THREE from 'three';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst GLBUploader = ({\n  onModelLoad,\n  onError,\n  currentScene,\n  productRef\n}) => {\n  _s();\n  const [isUploading, setIsUploading] = useState(false);\n  const [uploadedModel, setUploadedModel] = useState(null);\n  const [modelInfo, setModelInfo] = useState(null);\n  const fileInputRef = useRef(null);\n  const loaderRef = useRef(new GLTFLoader());\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    // Validate file type\n    if (!file.name.toLowerCase().endsWith('.glb') && !file.name.toLowerCase().endsWith('.gltf')) {\n      onError === null || onError === void 0 ? void 0 : onError('Please select a valid GLB or GLTF file');\n      return;\n    }\n\n    // Validate file size (max 50MB)\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    if (file.size > maxSize) {\n      onError === null || onError === void 0 ? void 0 : onError('File size too large. Please select a file smaller than 50MB');\n      return;\n    }\n    loadGLBFile(file);\n  };\n  const loadGLBFile = async file => {\n    setIsUploading(true);\n    try {\n      // Create URL for the file\n      const fileURL = URL.createObjectURL(file);\n\n      // Load the GLB/GLTF file\n      const gltf = await new Promise((resolve, reject) => {\n        loaderRef.current.load(fileURL, gltf => resolve(gltf), progress => {\n          console.log('Loading progress:', progress.loaded / progress.total * 100 + '%');\n        }, error => reject(error));\n      });\n\n      // Clean up the URL\n      URL.revokeObjectURL(fileURL);\n\n      // Process the loaded model\n      const model = gltf.scene;\n\n      // Get model information\n      const box = new THREE.Box3().setFromObject(model);\n      const size = box.getSize(new THREE.Vector3());\n      const center = box.getCenter(new THREE.Vector3());\n      const modelInfo = {\n        name: file.name,\n        size: file.size,\n        dimensions: {\n          width: size.x.toFixed(2),\n          height: size.y.toFixed(2),\n          depth: size.z.toFixed(2)\n        },\n        triangles: countTriangles(model),\n        materials: countMaterials(model)\n      };\n\n      // Center the model\n      model.position.sub(center);\n\n      // Scale model to reasonable size (optional)\n      const maxDimension = Math.max(size.x, size.y, size.z);\n      if (maxDimension > 5) {\n        const scale = 5 / maxDimension;\n        model.scale.setScalar(scale);\n      }\n\n      // Enable shadows for all meshes\n      model.traverse(child => {\n        if (child.isMesh) {\n          child.castShadow = true;\n          child.receiveShadow = true;\n\n          // Ensure materials are properly configured\n          if (child.material) {\n            child.material.needsUpdate = true;\n          }\n        }\n      });\n\n      // Replace current product with uploaded model\n      if (currentScene && productRef.current) {\n        // Remove existing product\n        currentScene.remove(productRef.current);\n\n        // Add new model\n        currentScene.add(model);\n        productRef.current = model;\n      }\n      setUploadedModel(model);\n      setModelInfo(modelInfo);\n      onModelLoad === null || onModelLoad === void 0 ? void 0 : onModelLoad(model, modelInfo);\n    } catch (error) {\n      console.error('Error loading GLB file:', error);\n      onError === null || onError === void 0 ? void 0 : onError('Failed to load 3D model. Please check the file format and try again.');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const countTriangles = object => {\n    let triangles = 0;\n    object.traverse(child => {\n      if (child.isMesh && child.geometry) {\n        const geometry = child.geometry;\n        if (geometry.index) {\n          triangles += geometry.index.count / 3;\n        } else {\n          triangles += geometry.attributes.position.count / 3;\n        }\n      }\n    });\n    return Math.floor(triangles);\n  };\n  const countMaterials = object => {\n    const materials = new Set();\n    object.traverse(child => {\n      if (child.isMesh && child.material) {\n        if (Array.isArray(child.material)) {\n          child.material.forEach(mat => materials.add(mat.uuid));\n        } else {\n          materials.add(child.material.uuid);\n        }\n      }\n    });\n    return materials.size;\n  };\n  const removeUploadedModel = () => {\n    if (currentScene && productRef.current && uploadedModel) {\n      currentScene.remove(productRef.current);\n      setUploadedModel(null);\n      setModelInfo(null);\n\n      // Notify parent to recreate default product\n      onModelLoad === null || onModelLoad === void 0 ? void 0 : onModelLoad(null, null);\n    }\n  };\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"glb-uploader\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"upload-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Custom 3D Model\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Upload your own GLB/GLTF file to replace the default model\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"upload-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \".glb,.gltf\",\n          onChange: handleFileSelect,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"upload-btn\",\n          onClick: () => {\n            var _fileInputRef$current;\n            return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n          },\n          disabled: isUploading,\n          children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 33\n            }, this), \"Uploading...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                points: \"7,10 12,15 17,10\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"12\",\n                y1: \"15\",\n                x2: \"12\",\n                y2: \"3\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 33\n            }, this), \"Upload GLB/GLTF\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 21\n        }, this), uploadedModel && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"remove-btn\",\n          onClick: removeUploadedModel,\n          title: \"Remove uploaded model\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M18 6L6 18M6 6l12 12\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 29\n          }, this), \"Remove\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 17\n      }, this), modelInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"model-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          children: \"Model Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"File:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: modelInfo.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Size:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: formatFileSize(modelInfo.size)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Dimensions:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: [modelInfo.dimensions.width, \" \\xD7 \", modelInfo.dimensions.height, \" \\xD7 \", modelInfo.dimensions.depth]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Triangles:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: modelInfo.triangles.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Materials:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: modelInfo.materials\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"upload-tips\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          children: \"Tips for best results:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Use GLB format for better compression\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Keep file size under 50MB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Optimize textures for web use\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Center your model at origin (0,0,0)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 9\n  }, this);\n};\n_s(GLBUploader, \"KcrShOGH7MpjxRAByvfrXVra2vI=\");\n_c = GLBUploader;\nexport default GLBUploader;\nvar _c;\n$RefreshReg$(_c, \"GLBUploader\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "GLTFLoader", "THREE", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "GLBUploader", "onModelLoad", "onError", "currentScene", "productRef", "_s", "isUploading", "setIsUploading", "uploadedModel", "setUploadedModel", "modelInfo", "setModelInfo", "fileInputRef", "loaderRef", "handleFileSelect", "event", "file", "target", "files", "name", "toLowerCase", "endsWith", "maxSize", "size", "loadGLBFile", "fileURL", "URL", "createObjectURL", "gltf", "Promise", "resolve", "reject", "current", "load", "progress", "console", "log", "loaded", "total", "error", "revokeObjectURL", "model", "scene", "box", "Box3", "setFromObject", "getSize", "Vector3", "center", "getCenter", "dimensions", "width", "x", "toFixed", "height", "y", "depth", "z", "triangles", "count<PERSON><PERSON><PERSON>", "materials", "countMaterials", "position", "sub", "maxDimension", "Math", "max", "scale", "setScalar", "traverse", "child", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "material", "needsUpdate", "remove", "add", "object", "geometry", "index", "count", "attributes", "floor", "Set", "Array", "isArray", "for<PERSON>ach", "mat", "uuid", "removeUploadedModel", "formatFileSize", "bytes", "k", "sizes", "i", "parseFloat", "pow", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "type", "accept", "onChange", "style", "display", "onClick", "_fileInputRef$current", "click", "disabled", "viewBox", "fill", "d", "stroke", "strokeWidth", "points", "x1", "y1", "x2", "y2", "title", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/3d/GLBUploader.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport * as THREE from 'three';\n\nconst GLBUploader = ({ onModelLoad, onError, currentScene, productRef }) => {\n    const [isUploading, setIsUploading] = useState(false);\n    const [uploadedModel, setUploadedModel] = useState(null);\n    const [modelInfo, setModelInfo] = useState(null);\n    const fileInputRef = useRef(null);\n    const loaderRef = useRef(new GLTFLoader());\n\n    const handleFileSelect = (event) => {\n        const file = event.target.files[0];\n        if (!file) return;\n\n        // Validate file type\n        if (!file.name.toLowerCase().endsWith('.glb') && !file.name.toLowerCase().endsWith('.gltf')) {\n            onError?.('Please select a valid GLB or GLTF file');\n            return;\n        }\n\n        // Validate file size (max 50MB)\n        const maxSize = 50 * 1024 * 1024; // 50MB\n        if (file.size > maxSize) {\n            onError?.('File size too large. Please select a file smaller than 50MB');\n            return;\n        }\n\n        loadGLBFile(file);\n    };\n\n    const loadGLBFile = async (file) => {\n        setIsUploading(true);\n        \n        try {\n            // Create URL for the file\n            const fileURL = URL.createObjectURL(file);\n            \n            // Load the GLB/GLTF file\n            const gltf = await new Promise((resolve, reject) => {\n                loaderRef.current.load(\n                    fileURL,\n                    (gltf) => resolve(gltf),\n                    (progress) => {\n                        console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%');\n                    },\n                    (error) => reject(error)\n                );\n            });\n\n            // Clean up the URL\n            URL.revokeObjectURL(fileURL);\n\n            // Process the loaded model\n            const model = gltf.scene;\n            \n            // Get model information\n            const box = new THREE.Box3().setFromObject(model);\n            const size = box.getSize(new THREE.Vector3());\n            const center = box.getCenter(new THREE.Vector3());\n\n            const modelInfo = {\n                name: file.name,\n                size: file.size,\n                dimensions: {\n                    width: size.x.toFixed(2),\n                    height: size.y.toFixed(2),\n                    depth: size.z.toFixed(2)\n                },\n                triangles: countTriangles(model),\n                materials: countMaterials(model)\n            };\n\n            // Center the model\n            model.position.sub(center);\n            \n            // Scale model to reasonable size (optional)\n            const maxDimension = Math.max(size.x, size.y, size.z);\n            if (maxDimension > 5) {\n                const scale = 5 / maxDimension;\n                model.scale.setScalar(scale);\n            }\n\n            // Enable shadows for all meshes\n            model.traverse((child) => {\n                if (child.isMesh) {\n                    child.castShadow = true;\n                    child.receiveShadow = true;\n                    \n                    // Ensure materials are properly configured\n                    if (child.material) {\n                        child.material.needsUpdate = true;\n                    }\n                }\n            });\n\n            // Replace current product with uploaded model\n            if (currentScene && productRef.current) {\n                // Remove existing product\n                currentScene.remove(productRef.current);\n                \n                // Add new model\n                currentScene.add(model);\n                productRef.current = model;\n            }\n\n            setUploadedModel(model);\n            setModelInfo(modelInfo);\n            onModelLoad?.(model, modelInfo);\n\n        } catch (error) {\n            console.error('Error loading GLB file:', error);\n            onError?.('Failed to load 3D model. Please check the file format and try again.');\n        } finally {\n            setIsUploading(false);\n        }\n    };\n\n    const countTriangles = (object) => {\n        let triangles = 0;\n        object.traverse((child) => {\n            if (child.isMesh && child.geometry) {\n                const geometry = child.geometry;\n                if (geometry.index) {\n                    triangles += geometry.index.count / 3;\n                } else {\n                    triangles += geometry.attributes.position.count / 3;\n                }\n            }\n        });\n        return Math.floor(triangles);\n    };\n\n    const countMaterials = (object) => {\n        const materials = new Set();\n        object.traverse((child) => {\n            if (child.isMesh && child.material) {\n                if (Array.isArray(child.material)) {\n                    child.material.forEach(mat => materials.add(mat.uuid));\n                } else {\n                    materials.add(child.material.uuid);\n                }\n            }\n        });\n        return materials.size;\n    };\n\n    const removeUploadedModel = () => {\n        if (currentScene && productRef.current && uploadedModel) {\n            currentScene.remove(productRef.current);\n            setUploadedModel(null);\n            setModelInfo(null);\n            \n            // Notify parent to recreate default product\n            onModelLoad?.(null, null);\n        }\n    };\n\n    const formatFileSize = (bytes) => {\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n\n    return (\n        <div className=\"glb-uploader\">\n            <div className=\"upload-section\">\n                <h4>Custom 3D Model</h4>\n                <p>Upload your own GLB/GLTF file to replace the default model</p>\n                \n                <div className=\"upload-controls\">\n                    <input\n                        ref={fileInputRef}\n                        type=\"file\"\n                        accept=\".glb,.gltf\"\n                        onChange={handleFileSelect}\n                        style={{ display: 'none' }}\n                    />\n                    \n                    <button\n                        className=\"upload-btn\"\n                        onClick={() => fileInputRef.current?.click()}\n                        disabled={isUploading}\n                    >\n                        {isUploading ? (\n                            <>\n                                <div className=\"spinner\"></div>\n                                Uploading...\n                            </>\n                        ) : (\n                            <>\n                                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <polyline points=\"7,10 12,15 17,10\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                                Upload GLB/GLTF\n                            </>\n                        )}\n                    </button>\n\n                    {uploadedModel && (\n                        <button\n                            className=\"remove-btn\"\n                            onClick={removeUploadedModel}\n                            title=\"Remove uploaded model\"\n                        >\n                            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                <path d=\"M18 6L6 18M6 6l12 12\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                            </svg>\n                            Remove\n                        </button>\n                    )}\n                </div>\n\n                {modelInfo && (\n                    <div className=\"model-info\">\n                        <h5>Model Information</h5>\n                        <div className=\"info-grid\">\n                            <div className=\"info-item\">\n                                <span className=\"label\">File:</span>\n                                <span className=\"value\">{modelInfo.name}</span>\n                            </div>\n                            <div className=\"info-item\">\n                                <span className=\"label\">Size:</span>\n                                <span className=\"value\">{formatFileSize(modelInfo.size)}</span>\n                            </div>\n                            <div className=\"info-item\">\n                                <span className=\"label\">Dimensions:</span>\n                                <span className=\"value\">\n                                    {modelInfo.dimensions.width} × {modelInfo.dimensions.height} × {modelInfo.dimensions.depth}\n                                </span>\n                            </div>\n                            <div className=\"info-item\">\n                                <span className=\"label\">Triangles:</span>\n                                <span className=\"value\">{modelInfo.triangles.toLocaleString()}</span>\n                            </div>\n                            <div className=\"info-item\">\n                                <span className=\"label\">Materials:</span>\n                                <span className=\"value\">{modelInfo.materials}</span>\n                            </div>\n                        </div>\n                    </div>\n                )}\n\n                <div className=\"upload-tips\">\n                    <h5>Tips for best results:</h5>\n                    <ul>\n                        <li>Use GLB format for better compression</li>\n                        <li>Keep file size under 50MB</li>\n                        <li>Optimize textures for web use</li>\n                        <li>Center your model at origin (0,0,0)</li>\n                    </ul>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default GLBUploader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,UAAU,QAAQ,uCAAuC;AAClE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,WAAW;EAAEC,OAAO;EAAEC,YAAY;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAMoB,YAAY,GAAGnB,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMoB,SAAS,GAAGpB,MAAM,CAAC,IAAIC,UAAU,CAAC,CAAC,CAAC;EAE1C,MAAMoB,gBAAgB,GAAIC,KAAK,IAAK;IAChC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACF,IAAI,EAAE;;IAEX;IACA,IAAI,CAACA,IAAI,CAACG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAACL,IAAI,CAACG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzFnB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,wCAAwC,CAAC;MACnD;IACJ;;IAEA;IACA,MAAMoB,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,IAAIN,IAAI,CAACO,IAAI,GAAGD,OAAO,EAAE;MACrBpB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,6DAA6D,CAAC;MACxE;IACJ;IAEAsB,WAAW,CAACR,IAAI,CAAC;EACrB,CAAC;EAED,MAAMQ,WAAW,GAAG,MAAOR,IAAI,IAAK;IAChCT,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACA;MACA,MAAMkB,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACX,IAAI,CAAC;;MAEzC;MACA,MAAMY,IAAI,GAAG,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QAChDlB,SAAS,CAACmB,OAAO,CAACC,IAAI,CAClBR,OAAO,EACNG,IAAI,IAAKE,OAAO,CAACF,IAAI,CAAC,EACtBM,QAAQ,IAAK;UACVC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAGF,QAAQ,CAACG,MAAM,GAAGH,QAAQ,CAACI,KAAK,GAAG,GAAG,GAAI,GAAG,CAAC;QACpF,CAAC,EACAC,KAAK,IAAKR,MAAM,CAACQ,KAAK,CAC3B,CAAC;MACL,CAAC,CAAC;;MAEF;MACAb,GAAG,CAACc,eAAe,CAACf,OAAO,CAAC;;MAE5B;MACA,MAAMgB,KAAK,GAAGb,IAAI,CAACc,KAAK;;MAExB;MACA,MAAMC,GAAG,GAAG,IAAIhD,KAAK,CAACiD,IAAI,CAAC,CAAC,CAACC,aAAa,CAACJ,KAAK,CAAC;MACjD,MAAMlB,IAAI,GAAGoB,GAAG,CAACG,OAAO,CAAC,IAAInD,KAAK,CAACoD,OAAO,CAAC,CAAC,CAAC;MAC7C,MAAMC,MAAM,GAAGL,GAAG,CAACM,SAAS,CAAC,IAAItD,KAAK,CAACoD,OAAO,CAAC,CAAC,CAAC;MAEjD,MAAMrC,SAAS,GAAG;QACdS,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfI,IAAI,EAAEP,IAAI,CAACO,IAAI;QACf2B,UAAU,EAAE;UACRC,KAAK,EAAE5B,IAAI,CAAC6B,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;UACxBC,MAAM,EAAE/B,IAAI,CAACgC,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC;UACzBG,KAAK,EAAEjC,IAAI,CAACkC,CAAC,CAACJ,OAAO,CAAC,CAAC;QAC3B,CAAC;QACDK,SAAS,EAAEC,cAAc,CAAClB,KAAK,CAAC;QAChCmB,SAAS,EAAEC,cAAc,CAACpB,KAAK;MACnC,CAAC;;MAED;MACAA,KAAK,CAACqB,QAAQ,CAACC,GAAG,CAACf,MAAM,CAAC;;MAE1B;MACA,MAAMgB,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC3C,IAAI,CAAC6B,CAAC,EAAE7B,IAAI,CAACgC,CAAC,EAAEhC,IAAI,CAACkC,CAAC,CAAC;MACrD,IAAIO,YAAY,GAAG,CAAC,EAAE;QAClB,MAAMG,KAAK,GAAG,CAAC,GAAGH,YAAY;QAC9BvB,KAAK,CAAC0B,KAAK,CAACC,SAAS,CAACD,KAAK,CAAC;MAChC;;MAEA;MACA1B,KAAK,CAAC4B,QAAQ,CAAEC,KAAK,IAAK;QACtB,IAAIA,KAAK,CAACC,MAAM,EAAE;UACdD,KAAK,CAACE,UAAU,GAAG,IAAI;UACvBF,KAAK,CAACG,aAAa,GAAG,IAAI;;UAE1B;UACA,IAAIH,KAAK,CAACI,QAAQ,EAAE;YAChBJ,KAAK,CAACI,QAAQ,CAACC,WAAW,GAAG,IAAI;UACrC;QACJ;MACJ,CAAC,CAAC;;MAEF;MACA,IAAIxE,YAAY,IAAIC,UAAU,CAAC4B,OAAO,EAAE;QACpC;QACA7B,YAAY,CAACyE,MAAM,CAACxE,UAAU,CAAC4B,OAAO,CAAC;;QAEvC;QACA7B,YAAY,CAAC0E,GAAG,CAACpC,KAAK,CAAC;QACvBrC,UAAU,CAAC4B,OAAO,GAAGS,KAAK;MAC9B;MAEAhC,gBAAgB,CAACgC,KAAK,CAAC;MACvB9B,YAAY,CAACD,SAAS,CAAC;MACvBT,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAGwC,KAAK,EAAE/B,SAAS,CAAC;IAEnC,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACZJ,OAAO,CAACI,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CrC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,sEAAsE,CAAC;IACrF,CAAC,SAAS;MACNK,cAAc,CAAC,KAAK,CAAC;IACzB;EACJ,CAAC;EAED,MAAMoD,cAAc,GAAImB,MAAM,IAAK;IAC/B,IAAIpB,SAAS,GAAG,CAAC;IACjBoB,MAAM,CAACT,QAAQ,CAAEC,KAAK,IAAK;MACvB,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACS,QAAQ,EAAE;QAChC,MAAMA,QAAQ,GAAGT,KAAK,CAACS,QAAQ;QAC/B,IAAIA,QAAQ,CAACC,KAAK,EAAE;UAChBtB,SAAS,IAAIqB,QAAQ,CAACC,KAAK,CAACC,KAAK,GAAG,CAAC;QACzC,CAAC,MAAM;UACHvB,SAAS,IAAIqB,QAAQ,CAACG,UAAU,CAACpB,QAAQ,CAACmB,KAAK,GAAG,CAAC;QACvD;MACJ;IACJ,CAAC,CAAC;IACF,OAAOhB,IAAI,CAACkB,KAAK,CAACzB,SAAS,CAAC;EAChC,CAAC;EAED,MAAMG,cAAc,GAAIiB,MAAM,IAAK;IAC/B,MAAMlB,SAAS,GAAG,IAAIwB,GAAG,CAAC,CAAC;IAC3BN,MAAM,CAACT,QAAQ,CAAEC,KAAK,IAAK;MACvB,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACI,QAAQ,EAAE;QAChC,IAAIW,KAAK,CAACC,OAAO,CAAChB,KAAK,CAACI,QAAQ,CAAC,EAAE;UAC/BJ,KAAK,CAACI,QAAQ,CAACa,OAAO,CAACC,GAAG,IAAI5B,SAAS,CAACiB,GAAG,CAACW,GAAG,CAACC,IAAI,CAAC,CAAC;QAC1D,CAAC,MAAM;UACH7B,SAAS,CAACiB,GAAG,CAACP,KAAK,CAACI,QAAQ,CAACe,IAAI,CAAC;QACtC;MACJ;IACJ,CAAC,CAAC;IACF,OAAO7B,SAAS,CAACrC,IAAI;EACzB,CAAC;EAED,MAAMmE,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIvF,YAAY,IAAIC,UAAU,CAAC4B,OAAO,IAAIxB,aAAa,EAAE;MACrDL,YAAY,CAACyE,MAAM,CAACxE,UAAU,CAAC4B,OAAO,CAAC;MACvCvB,gBAAgB,CAAC,IAAI,CAAC;MACtBE,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAV,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAG,IAAI,EAAE,IAAI,CAAC;IAC7B;EACJ,CAAC;EAED,MAAM0F,cAAc,GAAIC,KAAK,IAAK;IAC9B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAG9B,IAAI,CAACkB,KAAK,CAAClB,IAAI,CAAC7B,GAAG,CAACwD,KAAK,CAAC,GAAG3B,IAAI,CAAC7B,GAAG,CAACyD,CAAC,CAAC,CAAC;IACnD,OAAOG,UAAU,CAAC,CAACJ,KAAK,GAAG3B,IAAI,CAACgC,GAAG,CAACJ,CAAC,EAAEE,CAAC,CAAC,EAAE1C,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGyC,KAAK,CAACC,CAAC,CAAC;EAC3E,CAAC;EAED,oBACIlG,OAAA;IAAKqG,SAAS,EAAC,cAAc;IAAAC,QAAA,eACzBtG,OAAA;MAAKqG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3BtG,OAAA;QAAAsG,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxB1G,OAAA;QAAAsG,QAAA,EAAG;MAA0D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEjE1G,OAAA;QAAKqG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BtG,OAAA;UACI2G,GAAG,EAAE5F,YAAa;UAClB6F,IAAI,EAAC,MAAM;UACXC,MAAM,EAAC,YAAY;UACnBC,QAAQ,EAAE7F,gBAAiB;UAC3B8F,KAAK,EAAE;YAAEC,OAAO,EAAE;UAAO;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eAEF1G,OAAA;UACIqG,SAAS,EAAC,YAAY;UACtBY,OAAO,EAAEA,CAAA;YAAA,IAAAC,qBAAA;YAAA,QAAAA,qBAAA,GAAMnG,YAAY,CAACoB,OAAO,cAAA+E,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;UAAA,CAAC;UAC7CC,QAAQ,EAAE3G,WAAY;UAAA6F,QAAA,EAErB7F,WAAW,gBACRT,OAAA,CAAAE,SAAA;YAAAoG,QAAA,gBACItG,OAAA;cAAKqG,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAEnC;UAAA,eAAE,CAAC,gBAEH1G,OAAA,CAAAE,SAAA;YAAAoG,QAAA,gBACItG,OAAA;cAAKsD,KAAK,EAAC,IAAI;cAACG,MAAM,EAAC,IAAI;cAAC4D,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAAhB,QAAA,gBACvDtG,OAAA;gBAAMuH,CAAC,EAAC,2CAA2C;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC;cAAG;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAC3F1G,OAAA;gBAAU0H,MAAM,EAAC,kBAAkB;gBAACF,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC;cAAG;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAC3E1G,OAAA;gBAAM2H,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACN,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC;cAAG;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,mBAEV;UAAA,eAAE;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,EAER/F,aAAa,iBACVX,OAAA;UACIqG,SAAS,EAAC,YAAY;UACtBY,OAAO,EAAEpB,mBAAoB;UAC7BkC,KAAK,EAAC,uBAAuB;UAAAzB,QAAA,gBAE7BtG,OAAA;YAAKsD,KAAK,EAAC,IAAI;YAACG,MAAM,EAAC,IAAI;YAAC4D,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAAhB,QAAA,eACvDtG,OAAA;cAAMuH,CAAC,EAAC,sBAAsB;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC;YAAG;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,UAEV;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAEL7F,SAAS,iBACNb,OAAA;QAAKqG,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvBtG,OAAA;UAAAsG,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1B1G,OAAA;UAAKqG,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBtG,OAAA;YAAKqG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBtG,OAAA;cAAMqG,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpC1G,OAAA;cAAMqG,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAEzF,SAAS,CAACS;YAAI;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACN1G,OAAA;YAAKqG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBtG,OAAA;cAAMqG,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpC1G,OAAA;cAAMqG,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAER,cAAc,CAACjF,SAAS,CAACa,IAAI;YAAC;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACN1G,OAAA;YAAKqG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBtG,OAAA;cAAMqG,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1C1G,OAAA;cAAMqG,SAAS,EAAC,OAAO;cAAAC,QAAA,GAClBzF,SAAS,CAACwC,UAAU,CAACC,KAAK,EAAC,QAAG,EAACzC,SAAS,CAACwC,UAAU,CAACI,MAAM,EAAC,QAAG,EAAC5C,SAAS,CAACwC,UAAU,CAACM,KAAK;YAAA;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN1G,OAAA;YAAKqG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBtG,OAAA;cAAMqG,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzC1G,OAAA;cAAMqG,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAEzF,SAAS,CAACgD,SAAS,CAACmE,cAAc,CAAC;YAAC;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACN1G,OAAA;YAAKqG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBtG,OAAA;cAAMqG,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzC1G,OAAA;cAAMqG,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAEzF,SAAS,CAACkD;YAAS;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,eAED1G,OAAA;QAAKqG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBtG,OAAA;UAAAsG,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/B1G,OAAA;UAAAsG,QAAA,gBACItG,OAAA;YAAAsG,QAAA,EAAI;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9C1G,OAAA;YAAAsG,QAAA,EAAI;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClC1G,OAAA;YAAAsG,QAAA,EAAI;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtC1G,OAAA;YAAAsG,QAAA,EAAI;UAAmC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAClG,EAAA,CA/PIL,WAAW;AAAA8H,EAAA,GAAX9H,WAAW;AAiQjB,eAAeA,WAAW;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}