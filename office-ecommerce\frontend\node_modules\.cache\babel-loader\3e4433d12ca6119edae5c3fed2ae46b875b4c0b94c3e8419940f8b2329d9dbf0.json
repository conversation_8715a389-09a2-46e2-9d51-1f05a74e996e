{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\3d\\\\TableConfigurator.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport * as THREE from 'three';\nimport GLBUploader from './GLBUploader';\nimport '../../styles/configurator.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Advanced3DConfigurator = ({\n  onBack,\n  product\n}) => {\n  _s();\n  // 3D Scene refs\n  const mountRef = useRef(null);\n  const sceneRef = useRef(null);\n  const rendererRef = useRef(null);\n  const productRef = useRef(null);\n  const animationIdRef = useRef(null);\n  const cameraRef = useRef(null);\n\n  // Determine product type\n  const getProductType = () => {\n    if (!product) return 'table';\n    const name = product.name.toLowerCase();\n    if (name.includes('chair')) return 'chair';\n    if (name.includes('cabinet') || name.includes('storage')) return 'cabinet';\n    if (name.includes('shelf')) return 'shelf';\n    if (name.includes('workstation')) return 'workstation';\n    return 'table'; // default\n  };\n  const productType = getProductType();\n\n  // State for configuration\n  const [dimensions, setDimensions] = useState({\n    width: productType === 'chair' ? 60 : 280,\n    depth: productType === 'chair' ? 60 : 140,\n    height: productType === 'chair' ? 80 : 75\n  });\n  const [colors, setColors] = useState({\n    primary: '#6B7280',\n    secondary: '#374151',\n    accent: '#FFFFFF'\n  });\n  const [material, setMaterial] = useState(productType === 'chair' ? 'mesh' : 'wood');\n  const [quantity, setQuantity] = useState(1);\n\n  // Mobile detection\n  const [isMobile, setIsMobile] = useState(false);\n  useEffect(() => {\n    const checkMobile = () => {\n      const mobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n      setIsMobile(mobile);\n    };\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Enhanced pricing logic\n  const getBasePrice = () => {\n    const basePrices = {\n      table: 500,\n      chair: 200,\n      cabinet: 800,\n      shelf: 300,\n      workstation: 1200\n    };\n    return basePrices[productType] || 500;\n  };\n  const calculatePrice = () => {\n    let price = getBasePrice();\n\n    // Size multiplier\n    const sizeMultiplier = dimensions.width * dimensions.depth * dimensions.height / 100000;\n    price *= Math.max(0.5, Math.min(2.0, sizeMultiplier));\n\n    // Material multiplier\n    const materialMultipliers = {\n      wood: 1.0,\n      metal: 1.2,\n      glass: 1.5,\n      plastic: 0.8,\n      leather: 1.8,\n      mesh: 1.1,\n      fabric: 0.9,\n      vinyl: 1.0\n    };\n    price *= materialMultipliers[material] || 1.0;\n    return price * quantity;\n  };\n  const getCurrentPrice = () => calculatePrice();\n\n  // Camera control state - moved outside useEffect for global access\n  const cameraControlsRef = useRef({\n    targetRotationX: 0,\n    targetRotationY: 0,\n    currentRotationX: 0,\n    currentRotationY: 0,\n    targetDistance: 6,\n    currentDistance: 6,\n    isAnimating: false\n  });\n\n  // View control functions\n  const resetView = viewType => {\n    if (!cameraRef.current) return;\n    const controls = cameraControlsRef.current;\n    controls.isAnimating = true;\n    switch (viewType) {\n      case 'front':\n        controls.targetRotationX = 0;\n        controls.targetRotationY = 0;\n        controls.targetDistance = 6;\n        break;\n      case 'side':\n        controls.targetRotationX = 0;\n        controls.targetRotationY = Math.PI / 2;\n        controls.targetDistance = 6;\n        break;\n      case 'top':\n        controls.targetRotationX = Math.PI / 2;\n        controls.targetRotationY = 0;\n        controls.targetDistance = 8;\n        break;\n      case 'iso':\n      default:\n        controls.targetRotationX = 0.3;\n        controls.targetRotationY = 0.8;\n        controls.targetDistance = 6;\n        break;\n    }\n  };\n  const adjustZoom = direction => {\n    if (!cameraRef.current || !cameraControlsRef.current) return;\n    const controls = cameraControlsRef.current;\n    const zoomAmount = direction * 1.5;\n    const newDistance = controls.targetDistance + zoomAmount;\n\n    // Smooth zoom with bounds checking\n    controls.targetDistance = Math.max(2.5, Math.min(15, newDistance));\n\n    // Add slight animation flag for smoother zoom\n    controls.isAnimating = true;\n    setTimeout(() => {\n      if (controls) controls.isAnimating = false;\n    }, 500);\n  };\n\n  // Create 3D product functions\n  const createTableGeometry = group => {\n    // Table top\n    const topGeometry = new THREE.BoxGeometry(dimensions.width / 100, 0.08, dimensions.depth / 100);\n    const topMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: material === 'glass' ? 0.1 : 0.7,\n      metalness: material === 'metal' ? 0.8 : 0.0,\n      transparent: material === 'glass',\n      opacity: material === 'glass' ? 0.8 : 1.0\n    });\n    const tableTop = new THREE.Mesh(topGeometry, topMaterial);\n    tableTop.position.y = dimensions.height / 100 - 0.04;\n    if (!isMobile) {\n      tableTop.castShadow = true;\n      tableTop.receiveShadow = true;\n    }\n    group.add(tableTop);\n\n    // Legs with mobile optimization\n    const legSegments = isMobile ? 8 : 12;\n    const legGeometry = new THREE.CylinderGeometry(0.03, 0.03, dimensions.height / 100 - 0.08, legSegments);\n    const legMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7,\n      metalness: material === 'metal' ? 0.8 : 0.0\n    });\n    const legPositions = [[-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1], [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1], [-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1], [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1]];\n    legPositions.forEach(pos => {\n      const leg = new THREE.Mesh(legGeometry, legMaterial);\n      leg.position.set(pos[0], pos[1], pos[2]);\n      if (!isMobile) {\n        leg.castShadow = true;\n      }\n      group.add(leg);\n    });\n  };\n  const createChairGeometry = group => {\n    // Seat\n    const seatGeometry = new THREE.BoxGeometry(dimensions.width / 100, 0.06, dimensions.depth / 100);\n    const seatMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const seat = new THREE.Mesh(seatGeometry, seatMaterial);\n    seat.position.y = 0.4;\n    if (!isMobile) {\n      seat.castShadow = true;\n    }\n    group.add(seat);\n\n    // Backrest\n    const backGeometry = new THREE.BoxGeometry(dimensions.width / 100, 0.02, dimensions.height / 150);\n    const backMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const backrest = new THREE.Mesh(backGeometry, backMaterial);\n    backrest.position.set(0, 0.6, -dimensions.depth / 200 + 0.01);\n    if (!isMobile) {\n      backrest.castShadow = true;\n    }\n    group.add(backrest);\n\n    // Base with mobile optimization\n    const baseSegments = isMobile ? 5 : 8;\n    const baseGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.04, baseSegments);\n    const baseMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const base = new THREE.Mesh(baseGeometry, baseMaterial);\n    base.position.y = 0.02;\n    if (!isMobile) {\n      base.castShadow = true;\n    }\n    group.add(base);\n  };\n  const createProduct = scene => {\n    const productGroup = new THREE.Group();\n    switch (productType) {\n      case 'table':\n        createTableGeometry(productGroup);\n        break;\n      case 'chair':\n        createChairGeometry(productGroup);\n        break;\n      default:\n        createTableGeometry(productGroup);\n    }\n    productRef.current = productGroup;\n    scene.add(productGroup);\n  };\n\n  // Update product materials and geometry\n  const updateProduct = () => {\n    if (!productRef.current || !sceneRef.current) return;\n\n    // Remove old product\n    sceneRef.current.remove(productRef.current);\n\n    // Create new product with updated configuration\n    createProduct(sceneRef.current);\n  };\n\n  // 3D Scene Setup\n  useEffect(() => {\n    if (!mountRef.current) return;\n\n    // Scene setup\n    const scene = new THREE.Scene();\n    scene.background = new THREE.Color(0xf5f5f5); // Gray background\n    sceneRef.current = scene;\n\n    // Camera setup\n    const camera = new THREE.PerspectiveCamera(75, mountRef.current.clientWidth / mountRef.current.clientHeight, 0.1, 1000);\n    camera.position.set(4, 3, 4);\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // Renderer setup with mobile optimizations\n    const renderer = new THREE.WebGLRenderer({\n      antialias: !isMobile,\n      alpha: true,\n      powerPreference: isMobile ? \"low-power\" : \"high-performance\"\n    });\n    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);\n    renderer.setPixelRatio(Math.min(window.devicePixelRatio, isMobile ? 1.5 : 2));\n    renderer.setClearColor(0xf5f5f5, 1.0);\n    renderer.shadowMap.enabled = !isMobile;\n    if (!isMobile) {\n      renderer.shadowMap.type = THREE.PCFSoftShadowMap;\n    }\n    mountRef.current.appendChild(renderer.domElement);\n    rendererRef.current = renderer;\n\n    // Lighting System with mobile optimizations\n    const ambientLight = new THREE.AmbientLight(0x404040, isMobile ? 0.8 : 0.6);\n    scene.add(ambientLight);\n    const directionalLight = new THREE.DirectionalLight(0xffffff, isMobile ? 0.8 : 1.0);\n    directionalLight.position.set(5, 5, 5);\n    if (!isMobile) {\n      directionalLight.castShadow = true;\n      directionalLight.shadow.mapSize.width = 1024;\n      directionalLight.shadow.mapSize.height = 1024;\n    }\n    scene.add(directionalLight);\n\n    // Mouse interaction variables\n    let isMouseDown = false;\n    let mouseX = 0;\n    let mouseY = 0;\n\n    // Get camera controls from ref\n    const controls = cameraControlsRef.current;\n\n    // Mouse event handlers\n    const onMouseDown = event => {\n      isMouseDown = true;\n      mouseX = event.clientX;\n      mouseY = event.clientY;\n    };\n    const onMouseMove = event => {\n      if (!isMouseDown) return;\n      const deltaX = event.clientX - mouseX;\n      const deltaY = event.clientY - mouseY;\n\n      // Increased sensitivity for better responsiveness\n      controls.targetRotationY += deltaX * 0.015;\n      controls.targetRotationX += deltaY * 0.015;\n      controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));\n      mouseX = event.clientX;\n      mouseY = event.clientY;\n    };\n    const onMouseUp = () => {\n      isMouseDown = false;\n    };\n    const onWheel = event => {\n      event.preventDefault();\n      const zoomSpeed = isMobile ? 0.008 : 0.01;\n      controls.targetDistance += event.deltaY * zoomSpeed;\n      controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));\n    };\n\n    // Enhanced touch event handlers for mobile\n    let lastTouchDistance = 0;\n    let initialTouchDistance = 0;\n    const getTouchDistance = touches => {\n      if (touches.length < 2) return 0;\n      const dx = touches[0].clientX - touches[1].clientX;\n      const dy = touches[0].clientY - touches[1].clientY;\n      return Math.sqrt(dx * dx + dy * dy);\n    };\n    const onTouchStart = event => {\n      if (event.touches.length === 1) {\n        isMouseDown = true;\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      } else if (event.touches.length === 2) {\n        isMouseDown = false;\n        initialTouchDistance = getTouchDistance(event.touches);\n        lastTouchDistance = initialTouchDistance;\n      }\n    };\n    const onTouchMove = event => {\n      event.preventDefault();\n      if (event.touches.length === 1 && isMouseDown) {\n        // Single finger rotation with increased sensitivity\n        const deltaX = event.touches[0].clientX - mouseX;\n        const deltaY = event.touches[0].clientY - mouseY;\n        controls.targetRotationY += deltaX * 0.012;\n        controls.targetRotationX += deltaY * 0.012;\n        controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      } else if (event.touches.length === 2) {\n        // Two finger pinch zoom\n        const currentDistance = getTouchDistance(event.touches);\n        const deltaDistance = currentDistance - lastTouchDistance;\n        if (Math.abs(deltaDistance) > 3) {\n          // Increased threshold for smoother zoom\n          controls.targetDistance -= deltaDistance * 0.015;\n          controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));\n          lastTouchDistance = currentDistance;\n        }\n      }\n    };\n    const onTouchEnd = event => {\n      if (event.touches.length === 0) {\n        isMouseDown = false;\n      } else if (event.touches.length === 1) {\n        isMouseDown = true;\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      }\n    };\n\n    // Add event listeners to both canvas and document for better coverage\n    const canvas = renderer.domElement;\n\n    // Mouse events\n    canvas.addEventListener('mousedown', onMouseDown);\n    document.addEventListener('mousemove', onMouseMove); // Global mouse move for better tracking\n    document.addEventListener('mouseup', onMouseUp); // Global mouse up\n    canvas.addEventListener('wheel', onWheel, {\n      passive: false\n    });\n\n    // Touch events with better gesture handling\n    canvas.addEventListener('touchstart', onTouchStart, {\n      passive: false\n    });\n    canvas.addEventListener('touchmove', onTouchMove, {\n      passive: false\n    });\n    canvas.addEventListener('touchend', onTouchEnd, {\n      passive: false\n    });\n\n    // Prevent context menu on long press\n    canvas.addEventListener('contextmenu', e => e.preventDefault());\n\n    // Prevent default touch behaviors that might interfere\n    canvas.style.touchAction = 'none';\n    canvas.style.userSelect = 'none';\n\n    // Enhanced camera update function with smooth interpolation\n    const updateCamera = () => {\n      const lerpFactor = controls.isAnimating ? 0.15 : 0.08; // Faster when animating to presets\n\n      // Smooth interpolation\n      controls.currentRotationX += (controls.targetRotationX - controls.currentRotationX) * lerpFactor;\n      controls.currentRotationY += (controls.targetRotationY - controls.currentRotationY) * lerpFactor;\n      controls.currentDistance += (controls.targetDistance - controls.currentDistance) * lerpFactor;\n\n      // Calculate spherical coordinates for camera position\n      const x = Math.sin(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;\n      const y = Math.sin(controls.currentRotationX) * controls.currentDistance + 2;\n      const z = Math.cos(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;\n      camera.position.set(x, y, z);\n      camera.lookAt(0, 0, 0);\n\n      // Stop animation flag when close enough to target\n      if (controls.isAnimating) {\n        const rotXDiff = Math.abs(controls.targetRotationX - controls.currentRotationX);\n        const rotYDiff = Math.abs(controls.targetRotationY - controls.currentRotationY);\n        const distDiff = Math.abs(controls.targetDistance - controls.currentDistance);\n        if (rotXDiff < 0.01 && rotYDiff < 0.01 && distDiff < 0.1) {\n          controls.isAnimating = false;\n        }\n      }\n    };\n\n    // Initialize camera controls with default isometric view\n    controls.targetRotationX = 0.3;\n    controls.targetRotationY = 0.8;\n    controls.targetDistance = 6;\n    controls.currentRotationX = 0.3;\n    controls.currentRotationY = 0.8;\n    controls.currentDistance = 6;\n\n    // Create initial product\n    createProduct(scene);\n\n    // Animation loop\n    const animate = () => {\n      animationIdRef.current = requestAnimationFrame(animate);\n      updateCamera();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // Handle resize\n    const handleResize = () => {\n      if (!mountRef.current) return;\n      const width = mountRef.current.clientWidth;\n      const height = mountRef.current.clientHeight;\n      camera.aspect = width / height;\n      camera.updateProjectionMatrix();\n      renderer.setSize(width, height);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => {\n      window.removeEventListener('resize', handleResize);\n\n      // Remove canvas event listeners\n      const canvas = renderer.domElement;\n      canvas.removeEventListener('mousedown', onMouseDown);\n      canvas.removeEventListener('wheel', onWheel);\n      canvas.removeEventListener('touchstart', onTouchStart);\n      canvas.removeEventListener('touchmove', onTouchMove);\n      canvas.removeEventListener('touchend', onTouchEnd);\n      canvas.removeEventListener('contextmenu', e => e.preventDefault());\n\n      // Remove global event listeners\n      document.removeEventListener('mousemove', onMouseMove);\n      document.removeEventListener('mouseup', onMouseUp);\n      if (animationIdRef.current) {\n        cancelAnimationFrame(animationIdRef.current);\n      }\n      if (mountRef.current && renderer.domElement) {\n        mountRef.current.removeChild(renderer.domElement);\n      }\n      renderer.dispose();\n    };\n  }, [isMobile, dimensions, colors, material, productType]);\n\n  // Update product when configuration changes\n  useEffect(() => {\n    if (sceneRef.current && productRef.current) {\n      updateProduct();\n    }\n  }, [dimensions, colors, material]);\n  const handleAddToCart = () => {\n    const configuration = {\n      productType,\n      dimensions,\n      colors,\n      material,\n      quantity,\n      price: getCurrentPrice()\n    };\n    console.log('Adding to cart:', configuration);\n    alert(`Added ${quantity} ${productType}(s) to cart for $${getCurrentPrice().toFixed(2)}`);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"configurator-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"configurator-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"back-btn\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M19 12H5M12 19l-7-7 7-7\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this), \"Back to Products\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: [\"Advanced \", productType.charAt(0).toUpperCase() + productType.slice(1), \" Configurator\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 566,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"configurator-main\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"configurator-layout-horizontal\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"viewer-panel\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"config-card viewer-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n                      fill: \"#F0B21B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M2 17L12 22L22 17\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 589,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M2 12L12 17L22 12\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"3D Preview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Interactive model of your configured \", productType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"model-viewer-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"model-viewer\",\n                  ref: mountRef\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"viewer-controls-new\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"view-presets-horizontal\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"preset-btn-new\",\n                      onClick: () => resetView('front'),\n                      title: \"Front View\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                          x: \"4\",\n                          y: \"4\",\n                          width: \"16\",\n                          height: \"16\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\",\n                          rx: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 608,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"12\",\n                          cy: \"12\",\n                          r: \"1.5\",\n                          fill: \"currentColor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 609,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 607,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Front\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 611,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 602,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"preset-btn-new\",\n                      onClick: () => resetView('side'),\n                      title: \"Side View\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M4 12h16M12 4v16\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 619,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"12\",\n                          cy: \"12\",\n                          r: \"1.5\",\n                          fill: \"currentColor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 620,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 618,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Side\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 622,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 613,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"preset-btn-new\",\n                      onClick: () => resetView('top'),\n                      title: \"Top View\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 3L3 7L12 11L21 7L12 3Z\",\n                          fill: \"currentColor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 630,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M3 16L12 20L21 16\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 631,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 629,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Top\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 633,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 624,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"preset-btn-new\",\n                      onClick: () => resetView('iso'),\n                      title: \"3D View\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 3L3 7L12 11L21 7L12 3Z\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 641,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M3 16L12 20L21 16\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 642,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M3 11L12 15L21 11\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 643,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 640,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"3D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 645,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 635,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"zoom-controls-horizontal\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"zoom-btn-new\",\n                      onClick: () => adjustZoom(-1),\n                      title: \"Zoom Out\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"18\",\n                        height: \"18\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"11\",\n                          cy: \"11\",\n                          r: \"8\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 655,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M8 11h6\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 656,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M21 21l-4.35-4.35\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 657,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 654,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"zoom-btn-new\",\n                      onClick: () => adjustZoom(1),\n                      title: \"Zoom In\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"18\",\n                        height: \"18\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"11\",\n                          cy: \"11\",\n                          r: \"8\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 666,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M11 8v6M8 11h6\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 667,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M21 21l-4.35-4.35\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 668,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 665,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 660,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"viewer-instructions\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: isMobile ? \"Drag to rotate • Pinch to zoom • Use preset views\" : \"Drag to rotate • Scroll to zoom • Use preset views\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 674,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-info-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: (product === null || product === void 0 ? void 0 : product.name) || `Custom ${productType.charAt(0).toUpperCase() + productType.slice(1)}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"product-description\",\n                children: [\"Configure your perfect \", productType, \" with our advanced 3D customization tool. Adjust dimensions, choose materials, and see your changes in real-time.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-features\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"18\",\n                    height: \"18\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 694,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 695,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 693,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Real-time 3D visualization\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"18\",\n                    height: \"18\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 701,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 702,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 700,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Custom dimensions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"18\",\n                    height: \"18\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 708,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 707,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Premium materials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 711,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"18\",\n                    height: \"18\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 715,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 716,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 714,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Professional quality\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"config-panel\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"config-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M3 17h18M3 7h18M7 3v18M17 3v18\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 731,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M3 7l4-4 4 4M3 17l4 4 4-4M7 3l-4 4 4 4M17 3l4 4-4 4\",\n                      fill: \"#F0B21B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 732,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 730,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Dimensions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Adjust size to fit your space\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 737,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 735,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"dimension-controls-enhanced\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dimension-group-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dimension-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"dimension-label\",\n                      children: \"Width\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 743,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dimension-value-controls\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          width: Math.max(productType === 'chair' ? 50 : 100, dimensions.width - 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 750,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 749,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 745,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"dimension-display\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-value\",\n                          children: dimensions.width\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 754,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-unit\",\n                          children: \"cm\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 755,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 753,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          width: Math.min(productType === 'chair' ? 80 : 400, dimensions.width + 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12 5v14M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 762,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 761,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 757,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 744,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 742,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"range\",\n                    min: productType === 'chair' ? 50 : 100,\n                    max: productType === 'chair' ? 80 : 400,\n                    value: dimensions.width,\n                    onChange: e => setDimensions({\n                      ...dimensions,\n                      width: parseInt(e.target.value)\n                    }),\n                    className: \"dimension-slider-enhanced\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dimension-group-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dimension-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"dimension-label\",\n                      children: \"Depth\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 779,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dimension-value-controls\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          depth: Math.max(productType === 'chair' ? 50 : 60, dimensions.depth - 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 786,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 785,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 781,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"dimension-display\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-value\",\n                          children: dimensions.depth\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 790,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-unit\",\n                          children: \"cm\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 791,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 789,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          depth: Math.min(productType === 'chair' ? 80 : 200, dimensions.depth + 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12 5v14M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 798,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 797,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 793,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 780,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 778,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"range\",\n                    min: productType === 'chair' ? 50 : 60,\n                    max: productType === 'chair' ? 80 : 200,\n                    value: dimensions.depth,\n                    onChange: e => setDimensions({\n                      ...dimensions,\n                      depth: parseInt(e.target.value)\n                    }),\n                    className: \"dimension-slider-enhanced\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 803,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dimension-group-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dimension-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"dimension-label\",\n                      children: \"Height\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 815,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dimension-value-controls\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          height: Math.max(productType === 'chair' ? 70 : 60, dimensions.height - 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 822,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 821,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 817,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"dimension-display\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-value\",\n                          children: dimensions.height\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 826,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-unit\",\n                          children: \"cm\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 827,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 825,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          height: Math.min(productType === 'chair' ? 120 : 120, dimensions.height + 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12 5v14M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 834,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 833,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 829,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 816,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 814,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"range\",\n                    min: productType === 'chair' ? 70 : 60,\n                    max: productType === 'chair' ? 120 : 120,\n                    value: dimensions.height,\n                    onChange: e => setDimensions({\n                      ...dimensions,\n                      height: parseInt(e.target.value)\n                    }),\n                    className: \"dimension-slider-enhanced\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 839,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 740,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"config-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"10\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 856,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 2a10 10 0 0 0 0 20 10 10 0 0 1 0-20z\",\n                      fill: \"#F0B21B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 857,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 855,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 854,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Colors & Materials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Choose colors and surface materials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 862,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"color-material-grid-enhanced\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"color-section-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"section-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      children: \"Primary Color\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"color-info\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"current-color-name\",\n                        children: colors.primary === '#6B7280' ? 'Slate Gray' : colors.primary === '#374151' ? 'Dark Gray' : colors.primary === '#1F2937' ? 'Charcoal' : colors.primary === '#111827' ? 'Black' : colors.primary === '#F3F4F6' ? 'Light Gray' : colors.primary === '#E5E7EB' ? 'Silver' : colors.primary === '#F0B21B' ? 'Golden Yellow' : colors.primary === '#DC2626' ? 'Red' : colors.primary === '#059669' ? 'Green' : colors.primary === '#2563EB' ? 'Blue' : colors.primary === '#7C3AED' ? 'Purple' : colors.primary === '#EA580C' ? 'Orange' : 'Custom'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 870,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"current-color-preview\",\n                        style: {\n                          backgroundColor: colors.primary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 884,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 869,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"color-palette-enhanced\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"color-group\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"color-group-label\",\n                        children: \"Neutrals\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 892,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"color-row\",\n                        children: ['#6B7280', '#374151', '#1F2937', '#111827', '#F3F4F6', '#E5E7EB'].map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: `color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`,\n                          style: {\n                            backgroundColor: color\n                          },\n                          onClick: () => setColors({\n                            ...colors,\n                            primary: color\n                          }),\n                          title: color === '#6B7280' ? 'Slate Gray' : color === '#374151' ? 'Dark Gray' : color === '#1F2937' ? 'Charcoal' : color === '#111827' ? 'Black' : color === '#F3F4F6' ? 'Light Gray' : 'Silver'\n                        }, color, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 895,\n                          columnNumber: 29\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 893,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 891,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"color-group\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"color-group-label\",\n                        children: \"Accent Colors\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 910,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"color-row\",\n                        children: ['#F0B21B', '#DC2626', '#059669', '#2563EB', '#7C3AED', '#EA580C'].map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: `color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`,\n                          style: {\n                            backgroundColor: color\n                          },\n                          onClick: () => setColors({\n                            ...colors,\n                            primary: color\n                          }),\n                          title: color === '#F0B21B' ? 'Golden Yellow' : color === '#DC2626' ? 'Red' : color === '#059669' ? 'Green' : color === '#2563EB' ? 'Blue' : color === '#7C3AED' ? 'Purple' : 'Orange'\n                        }, color, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 913,\n                          columnNumber: 29\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 911,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 909,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 866,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"material-section-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"section-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      children: \"Material & Finish\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 932,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"current-material-name\",\n                      children: material.charAt(0).toUpperCase() + material.slice(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 933,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 931,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"material-options-enhanced\",\n                    children: (productType === 'chair' ? [{\n                      name: 'Mesh',\n                      value: 'mesh',\n                      icon: '🕸️',\n                      desc: 'Breathable mesh fabric'\n                    }, {\n                      name: 'Fabric',\n                      value: 'fabric',\n                      icon: '🧵',\n                      desc: 'Soft upholstery fabric'\n                    }, {\n                      name: 'Leather',\n                      value: 'leather',\n                      icon: '🐄',\n                      desc: 'Premium leather finish'\n                    }] : [{\n                      name: 'Wood',\n                      value: 'wood',\n                      icon: '🌳',\n                      desc: 'Natural wood grain'\n                    }, {\n                      name: 'Metal',\n                      value: 'metal',\n                      icon: '⚙️',\n                      desc: 'Brushed metal finish'\n                    }, {\n                      name: 'Glass',\n                      value: 'glass',\n                      icon: '💎',\n                      desc: 'Tempered glass surface'\n                    }]).map(mat => /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `material-option-enhanced ${material === mat.value ? 'active' : ''}`,\n                      onClick: () => setMaterial(mat.value),\n                      title: mat.desc,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"material-icon\",\n                        children: mat.icon\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 953,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"material-info\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"material-name\",\n                          children: mat.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 955,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"material-desc\",\n                          children: mat.desc\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 956,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 954,\n                        columnNumber: 27\n                      }, this)]\n                    }, mat.value, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 947,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 930,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"config-card pricing-card-modern\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-icon-modern\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2M6.4 15a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm11.2 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4z\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 970,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 969,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 968,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Order Summary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 974,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Configure and add to cart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 975,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 973,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 967,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"order-content-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-summary-modern\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-info-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"product-icon\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                          x: \"3\",\n                          y: \"4\",\n                          width: \"18\",\n                          height: \"12\",\n                          rx: \"2\",\n                          stroke: \"#F0B21B\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 985,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M7 8h10M7 12h6\",\n                          stroke: \"#F0B21B\",\n                          strokeWidth: \"1.5\",\n                          strokeLinecap: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 986,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 984,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 983,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"product-details\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"product-name\",\n                        children: [\"Custom \", productType.charAt(0).toUpperCase() + productType.slice(1)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 990,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"product-specs\",\n                        children: [dimensions.width, \"\\xD7\", dimensions.depth, \"\\xD7\", dimensions.height, \"cm\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 991,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 989,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 982,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 981,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"quantity-section-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"section-label\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"18\",\n                      height: \"18\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M9 12l2 2 4-4\",\n                        stroke: \"#F0B21B\",\n                        strokeWidth: \"2\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1000,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"9\",\n                        stroke: \"#F0B21B\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1001,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 999,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Quantity\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1003,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 998,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"quantity-controls-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setQuantity(Math.max(1, quantity - 1)),\n                      className: \"quantity-btn-modern\",\n                      disabled: quantity <= 1,\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"16\",\n                        height: \"16\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5 12h14\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\",\n                          strokeLinecap: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1012,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1011,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1006,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"quantity-display-modern\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"quantity-number-modern\",\n                        children: quantity\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1016,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1015,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setQuantity(quantity + 1),\n                      className: \"quantity-btn-modern\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"16\",\n                        height: \"16\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 5v14M5 12h14\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\",\n                          strokeLinecap: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1023,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1022,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1018,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1005,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 997,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-breakdown-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-row-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"price-label-modern\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"16\",\n                        height: \"16\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"12\",\n                          cy: \"12\",\n                          r: \"10\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1034,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 6v6l4 2\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\",\n                          strokeLinecap: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1035,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1033,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Base Price\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1037,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1032,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"price-amount\",\n                      children: [\"$\", getBasePrice().toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1039,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1031,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-row-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"price-label-modern\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"16\",\n                        height: \"16\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1044,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                          points: \"3.27,6.96 12,12.01 20.73,6.96\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1045,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                          x1: \"12\",\n                          y1: \"22.08\",\n                          x2: \"12\",\n                          y2: \"12\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1046,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1043,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Material & Size\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1048,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1042,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"price-amount\",\n                      children: [\"+$\", (getCurrentPrice() / quantity - getBasePrice()).toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1050,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1041,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-divider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1052,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-row-modern total-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"price-label-modern total-label\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"18\",\n                        height: \"18\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 2L2 7l10 5 10-5-10-5z\",\n                          fill: \"#F0B21B\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1056,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M2 17l10 5 10-5\",\n                          stroke: \"#F0B21B\",\n                          strokeWidth: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1057,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M2 12l10 5 10-5\",\n                          stroke: \"#F0B21B\",\n                          strokeWidth: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1058,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1055,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Total (\", quantity, \" item\", quantity > 1 ? 's' : '', \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1060,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1054,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"price-amount-total\",\n                      children: [\"$\", getCurrentPrice().toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1062,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1030,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"add-to-cart-btn-modern\",\n                  onClick: handleAddToCart,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"btn-content-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"btn-icon-modern\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\",\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1071,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"9\",\n                          cy: \"20\",\n                          r: \"1\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1072,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"20\",\n                          cy: \"20\",\n                          r: \"1\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1073,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1070,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1069,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"btn-text-modern\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"btn-action-modern\",\n                        children: \"Add to Cart\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1077,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"btn-price-modern\",\n                        children: [\"$\", getCurrentPrice().toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1078,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1076,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1068,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1067,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 979,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 564,\n    columnNumber: 5\n  }, this);\n};\n_s(Advanced3DConfigurator, \"wnIpAkH/bHEUoPFN2VgMeIGUs28=\");\n_c = Advanced3DConfigurator;\nexport default Advanced3DConfigurator;\nvar _c;\n$RefreshReg$(_c, \"Advanced3DConfigurator\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "THREE", "GLBUploader", "jsxDEV", "_jsxDEV", "Advanced3DConfigurator", "onBack", "product", "_s", "mountRef", "sceneRef", "rendererRef", "productRef", "animationIdRef", "cameraRef", "getProductType", "name", "toLowerCase", "includes", "productType", "dimensions", "setDimensions", "width", "depth", "height", "colors", "setColors", "primary", "secondary", "accent", "material", "setMaterial", "quantity", "setQuantity", "isMobile", "setIsMobile", "checkMobile", "mobile", "window", "innerWidth", "test", "navigator", "userAgent", "addEventListener", "removeEventListener", "getBasePrice", "basePrices", "table", "chair", "cabinet", "shelf", "workstation", "calculatePrice", "price", "sizeMultiplier", "Math", "max", "min", "materialMultipliers", "wood", "metal", "glass", "plastic", "leather", "mesh", "fabric", "vinyl", "getCurrentPrice", "cameraControlsRef", "targetRotationX", "targetRotationY", "currentRotationX", "currentRotationY", "targetDistance", "currentDistance", "isAnimating", "resetView", "viewType", "current", "controls", "PI", "adjustZoom", "direction", "zoomAmount", "newDistance", "setTimeout", "createTableGeometry", "group", "topGeometry", "BoxGeometry", "topMaterial", "MeshStandardMaterial", "color", "roughness", "metalness", "transparent", "opacity", "tableTop", "<PERSON><PERSON>", "position", "y", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "add", "legSegments", "legGeometry", "CylinderGeometry", "legMaterial", "legPositions", "for<PERSON>ach", "pos", "leg", "set", "createChairGeometry", "seatGeometry", "seatMaterial", "seat", "backGeometry", "backMaterial", "backrest", "baseSegments", "baseGeometry", "baseMaterial", "base", "createProduct", "scene", "productGroup", "Group", "updateProduct", "remove", "Scene", "background", "Color", "camera", "PerspectiveCamera", "clientWidth", "clientHeight", "lookAt", "renderer", "WebGLRenderer", "antialias", "alpha", "powerPreference", "setSize", "setPixelRatio", "devicePixelRatio", "setClearColor", "shadowMap", "enabled", "type", "PCFSoftShadowMap", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight", "DirectionalLight", "shadow", "mapSize", "isMouseDown", "mouseX", "mouseY", "onMouseDown", "event", "clientX", "clientY", "onMouseMove", "deltaX", "deltaY", "onMouseUp", "onWheel", "preventDefault", "zoomSpeed", "lastTouchDistance", "initialTouchDistance", "getTouchDistance", "touches", "length", "dx", "dy", "sqrt", "onTouchStart", "onTouchMove", "deltaDistance", "abs", "onTouchEnd", "canvas", "document", "passive", "e", "style", "touchAction", "userSelect", "updateCamera", "lerpFactor", "x", "sin", "cos", "z", "rotXDiff", "rotYDiff", "distDiff", "animate", "requestAnimationFrame", "render", "handleResize", "aspect", "updateProjectionMatrix", "cancelAnimationFrame", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "handleAddToCart", "configuration", "console", "log", "alert", "toFixed", "className", "children", "onClick", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "char<PERSON>t", "toUpperCase", "slice", "ref", "title", "rx", "cx", "cy", "r", "value", "onChange", "parseInt", "target", "backgroundColor", "map", "icon", "desc", "mat", "disabled", "points", "x1", "y1", "x2", "y2", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/3d/TableConfigurator.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport * as THREE from 'three';\nimport GLBUploader from './GLBUploader';\nimport '../../styles/configurator.css';\n\nconst Advanced3DConfigurator = ({ onBack, product }) => {\n  // 3D Scene refs\n  const mountRef = useRef(null);\n  const sceneRef = useRef(null);\n  const rendererRef = useRef(null);\n  const productRef = useRef(null);\n  const animationIdRef = useRef(null);\n  const cameraRef = useRef(null);\n\n  // Determine product type\n  const getProductType = () => {\n    if (!product) return 'table';\n    const name = product.name.toLowerCase();\n    if (name.includes('chair')) return 'chair';\n    if (name.includes('cabinet') || name.includes('storage')) return 'cabinet';\n    if (name.includes('shelf')) return 'shelf';\n    if (name.includes('workstation')) return 'workstation';\n    return 'table'; // default\n  };\n\n  const productType = getProductType();\n\n  // State for configuration\n  const [dimensions, setDimensions] = useState({\n    width: productType === 'chair' ? 60 : 280,\n    depth: productType === 'chair' ? 60 : 140,\n    height: productType === 'chair' ? 80 : 75\n  });\n\n  const [colors, setColors] = useState({\n    primary: '#6B7280',\n    secondary: '#374151',\n    accent: '#FFFFFF'\n  });\n\n  const [material, setMaterial] = useState(productType === 'chair' ? 'mesh' : 'wood');\n  const [quantity, setQuantity] = useState(1);\n\n  // Mobile detection\n  const [isMobile, setIsMobile] = useState(false);\n\n  useEffect(() => {\n    const checkMobile = () => {\n      const mobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n      setIsMobile(mobile);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Enhanced pricing logic\n  const getBasePrice = () => {\n    const basePrices = {\n      table: 500,\n      chair: 200,\n      cabinet: 800,\n      shelf: 300,\n      workstation: 1200\n    };\n    return basePrices[productType] || 500;\n  };\n\n  const calculatePrice = () => {\n    let price = getBasePrice();\n\n    // Size multiplier\n    const sizeMultiplier = (dimensions.width * dimensions.depth * dimensions.height) / 100000;\n    price *= Math.max(0.5, Math.min(2.0, sizeMultiplier));\n\n    // Material multiplier\n    const materialMultipliers = {\n      wood: 1.0,\n      metal: 1.2,\n      glass: 1.5,\n      plastic: 0.8,\n      leather: 1.8,\n      mesh: 1.1,\n      fabric: 0.9,\n      vinyl: 1.0\n    };\n    price *= materialMultipliers[material] || 1.0;\n\n    return price * quantity;\n  };\n\n  const getCurrentPrice = () => calculatePrice();\n\n  // Camera control state - moved outside useEffect for global access\n  const cameraControlsRef = useRef({\n    targetRotationX: 0,\n    targetRotationY: 0,\n    currentRotationX: 0,\n    currentRotationY: 0,\n    targetDistance: 6,\n    currentDistance: 6,\n    isAnimating: false\n  });\n\n  // View control functions\n  const resetView = (viewType) => {\n    if (!cameraRef.current) return;\n\n    const controls = cameraControlsRef.current;\n    controls.isAnimating = true;\n\n    switch (viewType) {\n      case 'front':\n        controls.targetRotationX = 0;\n        controls.targetRotationY = 0;\n        controls.targetDistance = 6;\n        break;\n      case 'side':\n        controls.targetRotationX = 0;\n        controls.targetRotationY = Math.PI / 2;\n        controls.targetDistance = 6;\n        break;\n      case 'top':\n        controls.targetRotationX = Math.PI / 2;\n        controls.targetRotationY = 0;\n        controls.targetDistance = 8;\n        break;\n      case 'iso':\n      default:\n        controls.targetRotationX = 0.3;\n        controls.targetRotationY = 0.8;\n        controls.targetDistance = 6;\n        break;\n    }\n  };\n\n  const adjustZoom = (direction) => {\n    if (!cameraRef.current || !cameraControlsRef.current) return;\n\n    const controls = cameraControlsRef.current;\n    const zoomAmount = direction * 1.5;\n    const newDistance = controls.targetDistance + zoomAmount;\n\n    // Smooth zoom with bounds checking\n    controls.targetDistance = Math.max(2.5, Math.min(15, newDistance));\n\n    // Add slight animation flag for smoother zoom\n    controls.isAnimating = true;\n    setTimeout(() => {\n      if (controls) controls.isAnimating = false;\n    }, 500);\n  };\n\n  // Create 3D product functions\n  const createTableGeometry = (group) => {\n    // Table top\n    const topGeometry = new THREE.BoxGeometry(\n      dimensions.width / 100,\n      0.08,\n      dimensions.depth / 100\n    );\n    const topMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: material === 'glass' ? 0.1 : 0.7,\n      metalness: material === 'metal' ? 0.8 : 0.0,\n      transparent: material === 'glass',\n      opacity: material === 'glass' ? 0.8 : 1.0\n    });\n\n    const tableTop = new THREE.Mesh(topGeometry, topMaterial);\n    tableTop.position.y = dimensions.height / 100 - 0.04;\n    if (!isMobile) {\n      tableTop.castShadow = true;\n      tableTop.receiveShadow = true;\n    }\n    group.add(tableTop);\n\n    // Legs with mobile optimization\n    const legSegments = isMobile ? 8 : 12;\n    const legGeometry = new THREE.CylinderGeometry(0.03, 0.03, dimensions.height / 100 - 0.08, legSegments);\n    const legMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7,\n      metalness: material === 'metal' ? 0.8 : 0.0\n    });\n\n    const legPositions = [\n      [-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1],\n      [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1],\n      [-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1],\n      [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1]\n    ];\n\n    legPositions.forEach(pos => {\n      const leg = new THREE.Mesh(legGeometry, legMaterial);\n      leg.position.set(pos[0], pos[1], pos[2]);\n      if (!isMobile) {\n        leg.castShadow = true;\n      }\n      group.add(leg);\n    });\n  };\n\n  const createChairGeometry = (group) => {\n    // Seat\n    const seatGeometry = new THREE.BoxGeometry(\n      dimensions.width / 100,\n      0.06,\n      dimensions.depth / 100\n    );\n    const seatMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const seat = new THREE.Mesh(seatGeometry, seatMaterial);\n    seat.position.y = 0.4;\n    if (!isMobile) {\n      seat.castShadow = true;\n    }\n    group.add(seat);\n\n    // Backrest\n    const backGeometry = new THREE.BoxGeometry(\n      dimensions.width / 100,\n      0.02,\n      dimensions.height / 150\n    );\n    const backMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const backrest = new THREE.Mesh(backGeometry, backMaterial);\n    backrest.position.set(0, 0.6, -dimensions.depth / 200 + 0.01);\n    if (!isMobile) {\n      backrest.castShadow = true;\n    }\n    group.add(backrest);\n\n    // Base with mobile optimization\n    const baseSegments = isMobile ? 5 : 8;\n    const baseGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.04, baseSegments);\n    const baseMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const base = new THREE.Mesh(baseGeometry, baseMaterial);\n    base.position.y = 0.02;\n    if (!isMobile) {\n      base.castShadow = true;\n    }\n    group.add(base);\n  };\n\n  const createProduct = (scene) => {\n    const productGroup = new THREE.Group();\n\n    switch (productType) {\n      case 'table':\n        createTableGeometry(productGroup);\n        break;\n      case 'chair':\n        createChairGeometry(productGroup);\n        break;\n      default:\n        createTableGeometry(productGroup);\n    }\n\n    productRef.current = productGroup;\n    scene.add(productGroup);\n  };\n\n  // Update product materials and geometry\n  const updateProduct = () => {\n    if (!productRef.current || !sceneRef.current) return;\n\n    // Remove old product\n    sceneRef.current.remove(productRef.current);\n\n    // Create new product with updated configuration\n    createProduct(sceneRef.current);\n  };\n\n  // 3D Scene Setup\n  useEffect(() => {\n    if (!mountRef.current) return;\n\n    // Scene setup\n    const scene = new THREE.Scene();\n    scene.background = new THREE.Color(0xf5f5f5); // Gray background\n    sceneRef.current = scene;\n\n    // Camera setup\n    const camera = new THREE.PerspectiveCamera(\n      75,\n      mountRef.current.clientWidth / mountRef.current.clientHeight,\n      0.1,\n      1000\n    );\n    camera.position.set(4, 3, 4);\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // Renderer setup with mobile optimizations\n    const renderer = new THREE.WebGLRenderer({\n      antialias: !isMobile,\n      alpha: true,\n      powerPreference: isMobile ? \"low-power\" : \"high-performance\"\n    });\n    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);\n    renderer.setPixelRatio(Math.min(window.devicePixelRatio, isMobile ? 1.5 : 2));\n    renderer.setClearColor(0xf5f5f5, 1.0);\n    renderer.shadowMap.enabled = !isMobile;\n    if (!isMobile) {\n      renderer.shadowMap.type = THREE.PCFSoftShadowMap;\n    }\n    mountRef.current.appendChild(renderer.domElement);\n    rendererRef.current = renderer;\n\n    // Lighting System with mobile optimizations\n    const ambientLight = new THREE.AmbientLight(0x404040, isMobile ? 0.8 : 0.6);\n    scene.add(ambientLight);\n\n    const directionalLight = new THREE.DirectionalLight(0xffffff, isMobile ? 0.8 : 1.0);\n    directionalLight.position.set(5, 5, 5);\n    if (!isMobile) {\n      directionalLight.castShadow = true;\n      directionalLight.shadow.mapSize.width = 1024;\n      directionalLight.shadow.mapSize.height = 1024;\n    }\n    scene.add(directionalLight);\n\n    // Mouse interaction variables\n    let isMouseDown = false;\n    let mouseX = 0;\n    let mouseY = 0;\n\n    // Get camera controls from ref\n    const controls = cameraControlsRef.current;\n\n    // Mouse event handlers\n    const onMouseDown = (event) => {\n      isMouseDown = true;\n      mouseX = event.clientX;\n      mouseY = event.clientY;\n    };\n\n    const onMouseMove = (event) => {\n      if (!isMouseDown) return;\n      const deltaX = event.clientX - mouseX;\n      const deltaY = event.clientY - mouseY;\n\n      // Increased sensitivity for better responsiveness\n      controls.targetRotationY += deltaX * 0.015;\n      controls.targetRotationX += deltaY * 0.015;\n\n      controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));\n\n      mouseX = event.clientX;\n      mouseY = event.clientY;\n    };\n\n    const onMouseUp = () => {\n      isMouseDown = false;\n    };\n\n    const onWheel = (event) => {\n      event.preventDefault();\n      const zoomSpeed = isMobile ? 0.008 : 0.01;\n      controls.targetDistance += event.deltaY * zoomSpeed;\n      controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));\n    };\n\n    // Enhanced touch event handlers for mobile\n    let lastTouchDistance = 0;\n    let initialTouchDistance = 0;\n\n    const getTouchDistance = (touches) => {\n      if (touches.length < 2) return 0;\n      const dx = touches[0].clientX - touches[1].clientX;\n      const dy = touches[0].clientY - touches[1].clientY;\n      return Math.sqrt(dx * dx + dy * dy);\n    };\n\n    const onTouchStart = (event) => {\n      if (event.touches.length === 1) {\n        isMouseDown = true;\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      } else if (event.touches.length === 2) {\n        isMouseDown = false;\n        initialTouchDistance = getTouchDistance(event.touches);\n        lastTouchDistance = initialTouchDistance;\n      }\n    };\n\n    const onTouchMove = (event) => {\n      event.preventDefault();\n\n      if (event.touches.length === 1 && isMouseDown) {\n        // Single finger rotation with increased sensitivity\n        const deltaX = event.touches[0].clientX - mouseX;\n        const deltaY = event.touches[0].clientY - mouseY;\n\n        controls.targetRotationY += deltaX * 0.012;\n        controls.targetRotationX += deltaY * 0.012;\n\n        controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));\n\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      } else if (event.touches.length === 2) {\n        // Two finger pinch zoom\n        const currentDistance = getTouchDistance(event.touches);\n        const deltaDistance = currentDistance - lastTouchDistance;\n\n        if (Math.abs(deltaDistance) > 3) { // Increased threshold for smoother zoom\n          controls.targetDistance -= deltaDistance * 0.015;\n          controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));\n          lastTouchDistance = currentDistance;\n        }\n      }\n    };\n\n    const onTouchEnd = (event) => {\n      if (event.touches.length === 0) {\n        isMouseDown = false;\n      } else if (event.touches.length === 1) {\n        isMouseDown = true;\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      }\n    };\n\n    // Add event listeners to both canvas and document for better coverage\n    const canvas = renderer.domElement;\n\n    // Mouse events\n    canvas.addEventListener('mousedown', onMouseDown);\n    document.addEventListener('mousemove', onMouseMove); // Global mouse move for better tracking\n    document.addEventListener('mouseup', onMouseUp); // Global mouse up\n    canvas.addEventListener('wheel', onWheel, { passive: false });\n\n    // Touch events with better gesture handling\n    canvas.addEventListener('touchstart', onTouchStart, { passive: false });\n    canvas.addEventListener('touchmove', onTouchMove, { passive: false });\n    canvas.addEventListener('touchend', onTouchEnd, { passive: false });\n\n    // Prevent context menu on long press\n    canvas.addEventListener('contextmenu', (e) => e.preventDefault());\n\n    // Prevent default touch behaviors that might interfere\n    canvas.style.touchAction = 'none';\n    canvas.style.userSelect = 'none';\n\n    // Enhanced camera update function with smooth interpolation\n    const updateCamera = () => {\n      const lerpFactor = controls.isAnimating ? 0.15 : 0.08; // Faster when animating to presets\n\n      // Smooth interpolation\n      controls.currentRotationX += (controls.targetRotationX - controls.currentRotationX) * lerpFactor;\n      controls.currentRotationY += (controls.targetRotationY - controls.currentRotationY) * lerpFactor;\n      controls.currentDistance += (controls.targetDistance - controls.currentDistance) * lerpFactor;\n\n      // Calculate spherical coordinates for camera position\n      const x = Math.sin(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;\n      const y = Math.sin(controls.currentRotationX) * controls.currentDistance + 2;\n      const z = Math.cos(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;\n\n      camera.position.set(x, y, z);\n      camera.lookAt(0, 0, 0);\n\n      // Stop animation flag when close enough to target\n      if (controls.isAnimating) {\n        const rotXDiff = Math.abs(controls.targetRotationX - controls.currentRotationX);\n        const rotYDiff = Math.abs(controls.targetRotationY - controls.currentRotationY);\n        const distDiff = Math.abs(controls.targetDistance - controls.currentDistance);\n\n        if (rotXDiff < 0.01 && rotYDiff < 0.01 && distDiff < 0.1) {\n          controls.isAnimating = false;\n        }\n      }\n    };\n\n    // Initialize camera controls with default isometric view\n    controls.targetRotationX = 0.3;\n    controls.targetRotationY = 0.8;\n    controls.targetDistance = 6;\n    controls.currentRotationX = 0.3;\n    controls.currentRotationY = 0.8;\n    controls.currentDistance = 6;\n\n    // Create initial product\n    createProduct(scene);\n\n    // Animation loop\n    const animate = () => {\n      animationIdRef.current = requestAnimationFrame(animate);\n      updateCamera();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // Handle resize\n    const handleResize = () => {\n      if (!mountRef.current) return;\n      const width = mountRef.current.clientWidth;\n      const height = mountRef.current.clientHeight;\n      camera.aspect = width / height;\n      camera.updateProjectionMatrix();\n      renderer.setSize(width, height);\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n\n      // Remove canvas event listeners\n      const canvas = renderer.domElement;\n      canvas.removeEventListener('mousedown', onMouseDown);\n      canvas.removeEventListener('wheel', onWheel);\n      canvas.removeEventListener('touchstart', onTouchStart);\n      canvas.removeEventListener('touchmove', onTouchMove);\n      canvas.removeEventListener('touchend', onTouchEnd);\n      canvas.removeEventListener('contextmenu', (e) => e.preventDefault());\n\n      // Remove global event listeners\n      document.removeEventListener('mousemove', onMouseMove);\n      document.removeEventListener('mouseup', onMouseUp);\n\n      if (animationIdRef.current) {\n        cancelAnimationFrame(animationIdRef.current);\n      }\n      if (mountRef.current && renderer.domElement) {\n        mountRef.current.removeChild(renderer.domElement);\n      }\n      renderer.dispose();\n    };\n  }, [isMobile, dimensions, colors, material, productType]);\n\n  // Update product when configuration changes\n  useEffect(() => {\n    if (sceneRef.current && productRef.current) {\n      updateProduct();\n    }\n  }, [dimensions, colors, material]);\n\n  const handleAddToCart = () => {\n    const configuration = {\n      productType,\n      dimensions,\n      colors,\n      material,\n      quantity,\n      price: getCurrentPrice()\n    };\n\n    console.log('Adding to cart:', configuration);\n    alert(`Added ${quantity} ${productType}(s) to cart for $${getCurrentPrice().toFixed(2)}`);\n  };\n\n  return (\n    <div className=\"configurator-container\">\n      {/* Header */}\n      <div className=\"configurator-header\">\n        <div className=\"container\">\n          <button onClick={onBack} className=\"back-btn\">\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n              <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n            Back to Products\n          </button>\n          <h1>Advanced {productType.charAt(0).toUpperCase() + productType.slice(1)} Configurator</h1>\n        </div>\n      </div>\n\n      {/* Main Configuration */}\n      <div className=\"configurator-main\">\n        <div className=\"container\">\n          <div className=\"configurator-layout-horizontal\">\n            {/* Left Side - 3D Model Viewer */}\n            <div className=\"viewer-panel\">\n              <div className=\"config-card viewer-card\">\n                <div className=\"card-header\">\n                  <div className=\"card-icon\">\n                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" fill=\"#F0B21B\"/>\n                      <path d=\"M2 17L12 22L22 17\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                      <path d=\"M2 12L12 17L22 12\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                  </div>\n                  <div className=\"card-title\">\n                    <h4>3D Preview</h4>\n                    <p>Interactive model of your configured {productType}</p>\n                  </div>\n                </div>\n                <div className=\"model-viewer-container\">\n                  <div className=\"model-viewer\" ref={mountRef}></div>\n                  <div className=\"viewer-controls-new\">\n                    <div className=\"view-presets-horizontal\">\n                      <button\n                        className=\"preset-btn-new\"\n                        onClick={() => resetView('front')}\n                        title=\"Front View\"\n                      >\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <rect x=\"4\" y=\"4\" width=\"16\" height=\"16\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\" rx=\"2\"/>\n                          <circle cx=\"12\" cy=\"12\" r=\"1.5\" fill=\"currentColor\"/>\n                        </svg>\n                        <span>Front</span>\n                      </button>\n                      <button\n                        className=\"preset-btn-new\"\n                        onClick={() => resetView('side')}\n                        title=\"Side View\"\n                      >\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M4 12h16M12 4v16\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <circle cx=\"12\" cy=\"12\" r=\"1.5\" fill=\"currentColor\"/>\n                        </svg>\n                        <span>Side</span>\n                      </button>\n                      <button\n                        className=\"preset-btn-new\"\n                        onClick={() => resetView('top')}\n                        title=\"Top View\"\n                      >\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M12 3L3 7L12 11L21 7L12 3Z\" fill=\"currentColor\"/>\n                          <path d=\"M3 16L12 20L21 16\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\"/>\n                        </svg>\n                        <span>Top</span>\n                      </button>\n                      <button\n                        className=\"preset-btn-new\"\n                        onClick={() => resetView('iso')}\n                        title=\"3D View\"\n                      >\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M12 3L3 7L12 11L21 7L12 3Z\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\"/>\n                          <path d=\"M3 16L12 20L21 16\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\"/>\n                          <path d=\"M3 11L12 15L21 11\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\"/>\n                        </svg>\n                        <span>3D</span>\n                      </button>\n                    </div>\n                    <div className=\"zoom-controls-horizontal\">\n                      <button\n                        className=\"zoom-btn-new\"\n                        onClick={() => adjustZoom(-1)}\n                        title=\"Zoom Out\"\n                      >\n                        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <path d=\"M8 11h6\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <path d=\"M21 21l-4.35-4.35\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                        </svg>\n                      </button>\n                      <button\n                        className=\"zoom-btn-new\"\n                        onClick={() => adjustZoom(1)}\n                        title=\"Zoom In\"\n                      >\n                        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <path d=\"M11 8v6M8 11h6\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <path d=\"M21 21l-4.35-4.35\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                        </svg>\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"viewer-instructions\">\n                    <span>\n                      {isMobile\n                        ? \"Drag to rotate • Pinch to zoom • Use preset views\"\n                        : \"Drag to rotate • Scroll to zoom • Use preset views\"\n                      }\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Product Info Below 3D Viewer */}\n              <div className=\"product-info-section\">\n                <h3>{product?.name || `Custom ${productType.charAt(0).toUpperCase() + productType.slice(1)}`}</h3>\n                <p className=\"product-description\">\n                  Configure your perfect {productType} with our advanced 3D customization tool.\n                  Adjust dimensions, choose materials, and see your changes in real-time.\n                </p>\n                <div className=\"product-features\">\n                  <div className=\"feature-item\">\n                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                    <span>Real-time 3D visualization</span>\n                  </div>\n                  <div className=\"feature-item\">\n                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                    <span>Custom dimensions</span>\n                  </div>\n                  <div className=\"feature-item\">\n                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                    <span>Premium materials</span>\n                  </div>\n                  <div className=\"feature-item\">\n                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                    <span>Professional quality</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Right Side - Configuration Panel */}\n            <div className=\"config-panel\">\n              {/* Dimensions */}\n              <div className=\"config-card\">\n                <div className=\"card-header\">\n                  <div className=\"card-icon\">\n                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M3 17h18M3 7h18M7 3v18M17 3v18\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                      <path d=\"M3 7l4-4 4 4M3 17l4 4 4-4M7 3l-4 4 4 4M17 3l4 4-4 4\" fill=\"#F0B21B\"/>\n                    </svg>\n                  </div>\n                  <div className=\"card-title\">\n                    <h4>Dimensions</h4>\n                    <p>Adjust size to fit your space</p>\n                  </div>\n                </div>\n                <div className=\"dimension-controls-enhanced\">\n                  <div className=\"dimension-group-enhanced\">\n                    <div className=\"dimension-header\">\n                      <label className=\"dimension-label\">Width</label>\n                      <div className=\"dimension-value-controls\">\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, width: Math.max(productType === 'chair' ? 50 : 100, dimensions.width - 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                        <div className=\"dimension-display\">\n                          <span className=\"dimension-value\">{dimensions.width}</span>\n                          <span className=\"dimension-unit\">cm</span>\n                        </div>\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, width: Math.min(productType === 'chair' ? 80 : 400, dimensions.width + 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M12 5v14M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                    <input\n                      type=\"range\"\n                      min={productType === 'chair' ? 50 : 100}\n                      max={productType === 'chair' ? 80 : 400}\n                      value={dimensions.width}\n                      onChange={(e) => setDimensions({...dimensions, width: parseInt(e.target.value)})}\n                      className=\"dimension-slider-enhanced\"\n                    />\n                  </div>\n\n                  <div className=\"dimension-group-enhanced\">\n                    <div className=\"dimension-header\">\n                      <label className=\"dimension-label\">Depth</label>\n                      <div className=\"dimension-value-controls\">\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, depth: Math.max(productType === 'chair' ? 50 : 60, dimensions.depth - 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                        <div className=\"dimension-display\">\n                          <span className=\"dimension-value\">{dimensions.depth}</span>\n                          <span className=\"dimension-unit\">cm</span>\n                        </div>\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, depth: Math.min(productType === 'chair' ? 80 : 200, dimensions.depth + 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M12 5v14M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                    <input\n                      type=\"range\"\n                      min={productType === 'chair' ? 50 : 60}\n                      max={productType === 'chair' ? 80 : 200}\n                      value={dimensions.depth}\n                      onChange={(e) => setDimensions({...dimensions, depth: parseInt(e.target.value)})}\n                      className=\"dimension-slider-enhanced\"\n                    />\n                  </div>\n\n                  <div className=\"dimension-group-enhanced\">\n                    <div className=\"dimension-header\">\n                      <label className=\"dimension-label\">Height</label>\n                      <div className=\"dimension-value-controls\">\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, height: Math.max(productType === 'chair' ? 70 : 60, dimensions.height - 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                        <div className=\"dimension-display\">\n                          <span className=\"dimension-value\">{dimensions.height}</span>\n                          <span className=\"dimension-unit\">cm</span>\n                        </div>\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, height: Math.min(productType === 'chair' ? 120 : 120, dimensions.height + 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M12 5v14M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                    <input\n                      type=\"range\"\n                      min={productType === 'chair' ? 70 : 60}\n                      max={productType === 'chair' ? 120 : 120}\n                      value={dimensions.height}\n                      onChange={(e) => setDimensions({...dimensions, height: parseInt(e.target.value)})}\n                      className=\"dimension-slider-enhanced\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Colors and Materials */}\n              <div className=\"config-card\">\n                <div className=\"card-header\">\n                  <div className=\"card-icon\">\n                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                      <path d=\"M12 2a10 10 0 0 0 0 20 10 10 0 0 1 0-20z\" fill=\"#F0B21B\"/>\n                    </svg>\n                  </div>\n                  <div className=\"card-title\">\n                    <h4>Colors & Materials</h4>\n                    <p>Choose colors and surface materials</p>\n                  </div>\n                </div>\n                <div className=\"color-material-grid-enhanced\">\n                  <div className=\"color-section-enhanced\">\n                    <div className=\"section-header\">\n                      <h5>Primary Color</h5>\n                      <div className=\"color-info\">\n                        <span className=\"current-color-name\">\n                          {colors.primary === '#6B7280' ? 'Slate Gray' :\n                           colors.primary === '#374151' ? 'Dark Gray' :\n                           colors.primary === '#1F2937' ? 'Charcoal' :\n                           colors.primary === '#111827' ? 'Black' :\n                           colors.primary === '#F3F4F6' ? 'Light Gray' :\n                           colors.primary === '#E5E7EB' ? 'Silver' :\n                           colors.primary === '#F0B21B' ? 'Golden Yellow' :\n                           colors.primary === '#DC2626' ? 'Red' :\n                           colors.primary === '#059669' ? 'Green' :\n                           colors.primary === '#2563EB' ? 'Blue' :\n                           colors.primary === '#7C3AED' ? 'Purple' :\n                           colors.primary === '#EA580C' ? 'Orange' : 'Custom'}\n                        </span>\n                        <div\n                          className=\"current-color-preview\"\n                          style={{ backgroundColor: colors.primary }}\n                        ></div>\n                      </div>\n                    </div>\n                    <div className=\"color-palette-enhanced\">\n                      <div className=\"color-group\">\n                        <span className=\"color-group-label\">Neutrals</span>\n                        <div className=\"color-row\">\n                          {['#6B7280', '#374151', '#1F2937', '#111827', '#F3F4F6', '#E5E7EB'].map((color) => (\n                            <button\n                              key={color}\n                              className={`color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`}\n                              style={{ backgroundColor: color }}\n                              onClick={() => setColors({...colors, primary: color})}\n                              title={color === '#6B7280' ? 'Slate Gray' :\n                                     color === '#374151' ? 'Dark Gray' :\n                                     color === '#1F2937' ? 'Charcoal' :\n                                     color === '#111827' ? 'Black' :\n                                     color === '#F3F4F6' ? 'Light Gray' : 'Silver'}\n                            />\n                          ))}\n                        </div>\n                      </div>\n                      <div className=\"color-group\">\n                        <span className=\"color-group-label\">Accent Colors</span>\n                        <div className=\"color-row\">\n                          {['#F0B21B', '#DC2626', '#059669', '#2563EB', '#7C3AED', '#EA580C'].map((color) => (\n                            <button\n                              key={color}\n                              className={`color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`}\n                              style={{ backgroundColor: color }}\n                              onClick={() => setColors({...colors, primary: color})}\n                              title={color === '#F0B21B' ? 'Golden Yellow' :\n                                     color === '#DC2626' ? 'Red' :\n                                     color === '#059669' ? 'Green' :\n                                     color === '#2563EB' ? 'Blue' :\n                                     color === '#7C3AED' ? 'Purple' : 'Orange'}\n                            />\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"material-section-enhanced\">\n                    <div className=\"section-header\">\n                      <h5>Material & Finish</h5>\n                      <span className=\"current-material-name\">\n                        {material.charAt(0).toUpperCase() + material.slice(1)}\n                      </span>\n                    </div>\n                    <div className=\"material-options-enhanced\">\n                      {(productType === 'chair' ? [\n                        { name: 'Mesh', value: 'mesh', icon: '🕸️', desc: 'Breathable mesh fabric' },\n                        { name: 'Fabric', value: 'fabric', icon: '🧵', desc: 'Soft upholstery fabric' },\n                        { name: 'Leather', value: 'leather', icon: '🐄', desc: 'Premium leather finish' }\n                      ] : [\n                        { name: 'Wood', value: 'wood', icon: '🌳', desc: 'Natural wood grain' },\n                        { name: 'Metal', value: 'metal', icon: '⚙️', desc: 'Brushed metal finish' },\n                        { name: 'Glass', value: 'glass', icon: '💎', desc: 'Tempered glass surface' }\n                      ]).map((mat) => (\n                        <button\n                          key={mat.value}\n                          className={`material-option-enhanced ${material === mat.value ? 'active' : ''}`}\n                          onClick={() => setMaterial(mat.value)}\n                          title={mat.desc}\n                        >\n                          <div className=\"material-icon\">{mat.icon}</div>\n                          <div className=\"material-info\">\n                            <span className=\"material-name\">{mat.name}</span>\n                            <span className=\"material-desc\">{mat.desc}</span>\n                          </div>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Modern Order Summary */}\n              <div className=\"config-card pricing-card-modern\">\n                <div className=\"card-header-modern\">\n                  <div className=\"card-icon-modern\">\n                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2M6.4 15a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm11.2 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4z\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                  </div>\n                  <div className=\"card-title-modern\">\n                    <h4>Order Summary</h4>\n                    <p>Configure and add to cart</p>\n                  </div>\n                </div>\n\n                <div className=\"order-content-modern\">\n                  {/* Product Summary */}\n                  <div className=\"product-summary-modern\">\n                    <div className=\"product-info-row\">\n                      <div className=\"product-icon\">\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <rect x=\"3\" y=\"4\" width=\"18\" height=\"12\" rx=\"2\" stroke=\"#F0B21B\" strokeWidth=\"1.5\" fill=\"none\"/>\n                          <path d=\"M7 8h10M7 12h6\" stroke=\"#F0B21B\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                        </svg>\n                      </div>\n                      <div className=\"product-details\">\n                        <span className=\"product-name\">Custom {productType.charAt(0).toUpperCase() + productType.slice(1)}</span>\n                        <span className=\"product-specs\">{dimensions.width}×{dimensions.depth}×{dimensions.height}cm</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Quantity Controls */}\n                  <div className=\"quantity-section-modern\">\n                    <div className=\"section-label\">\n                      <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\n                      </svg>\n                      <span>Quantity</span>\n                    </div>\n                    <div className=\"quantity-controls-modern\">\n                      <button\n                        onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                        className=\"quantity-btn-modern\"\n                        disabled={quantity <= 1}\n                      >\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        </svg>\n                      </button>\n                      <div className=\"quantity-display-modern\">\n                        <span className=\"quantity-number-modern\">{quantity}</span>\n                      </div>\n                      <button\n                        onClick={() => setQuantity(quantity + 1)}\n                        className=\"quantity-btn-modern\"\n                      >\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M12 5v14M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        </svg>\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Price Breakdown */}\n                  <div className=\"price-breakdown-modern\">\n                    <div className=\"price-row-modern\">\n                      <div className=\"price-label-modern\">\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#64748b\" strokeWidth=\"1.5\"/>\n                          <path d=\"M12 6v6l4 2\" stroke=\"#64748b\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                        </svg>\n                        <span>Base Price</span>\n                      </div>\n                      <span className=\"price-amount\">${getBasePrice().toFixed(2)}</span>\n                    </div>\n                    <div className=\"price-row-modern\">\n                      <div className=\"price-label-modern\">\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\" stroke=\"#64748b\" strokeWidth=\"1.5\"/>\n                          <polyline points=\"3.27,6.96 12,12.01 20.73,6.96\" stroke=\"#64748b\" strokeWidth=\"1.5\"/>\n                          <line x1=\"12\" y1=\"22.08\" x2=\"12\" y2=\"12\" stroke=\"#64748b\" strokeWidth=\"1.5\"/>\n                        </svg>\n                        <span>Material & Size</span>\n                      </div>\n                      <span className=\"price-amount\">+${(getCurrentPrice() / quantity - getBasePrice()).toFixed(2)}</span>\n                    </div>\n                    <div className=\"price-divider\"></div>\n                    <div className=\"price-row-modern total-row\">\n                      <div className=\"price-label-modern total-label\">\n                        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M12 2L2 7l10 5 10-5-10-5z\" fill=\"#F0B21B\"/>\n                          <path d=\"M2 17l10 5 10-5\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                          <path d=\"M2 12l10 5 10-5\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                        </svg>\n                        <span>Total ({quantity} item{quantity > 1 ? 's' : ''})</span>\n                      </div>\n                      <span className=\"price-amount-total\">${getCurrentPrice().toFixed(2)}</span>\n                    </div>\n                  </div>\n\n                  {/* Add to Cart Button */}\n                  <button className=\"add-to-cart-btn-modern\" onClick={handleAddToCart}>\n                    <div className=\"btn-content-modern\">\n                      <div className=\"btn-icon-modern\">\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"9\" cy=\"20\" r=\"1\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        <circle cx=\"20\" cy=\"20\" r=\"1\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        </svg>\n                      </div>\n                      <div className=\"btn-text-modern\">\n                        <span className=\"btn-action-modern\">Add to Cart</span>\n                        <span className=\"btn-price-modern\">${getCurrentPrice().toFixed(2)}</span>\n                      </div>\n                    </div>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Advanced3DConfigurator;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtD;EACA,MAAMC,QAAQ,GAAGV,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMW,QAAQ,GAAGX,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMY,WAAW,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMa,UAAU,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMc,cAAc,GAAGd,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMe,SAAS,GAAGf,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMgB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACR,OAAO,EAAE,OAAO,OAAO;IAC5B,MAAMS,IAAI,GAAGT,OAAO,CAACS,IAAI,CAACC,WAAW,CAAC,CAAC;IACvC,IAAID,IAAI,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,OAAO;IAC1C,IAAIF,IAAI,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,IAAI,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,SAAS;IAC1E,IAAIF,IAAI,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,OAAO;IAC1C,IAAIF,IAAI,CAACE,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,aAAa;IACtD,OAAO,OAAO,CAAC,CAAC;EAClB,CAAC;EAED,MAAMC,WAAW,GAAGJ,cAAc,CAAC,CAAC;;EAEpC;EACA,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC;IAC3CwB,KAAK,EAAEH,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG;IACzCI,KAAK,EAAEJ,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG;IACzCK,MAAM,EAAEL,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG;EACzC,CAAC,CAAC;EAEF,MAAM,CAACM,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC;IACnC6B,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAACqB,WAAW,KAAK,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC;EACnF,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;;EAE3C;EACA,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAE/CE,SAAS,CAAC,MAAM;IACd,MAAMoC,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMC,MAAM,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG,IAAI,gEAAgE,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;MACrIP,WAAW,CAACE,MAAM,CAAC;IACrB,CAAC;IAEDD,WAAW,CAAC,CAAC;IACbE,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAEP,WAAW,CAAC;IAC9C,OAAO,MAAME,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAER,WAAW,CAAC;EAChE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG;MACjBC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,GAAG;MACVC,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE,GAAG;MACVC,WAAW,EAAE;IACf,CAAC;IACD,OAAOL,UAAU,CAAC3B,WAAW,CAAC,IAAI,GAAG;EACvC,CAAC;EAED,MAAMiC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIC,KAAK,GAAGR,YAAY,CAAC,CAAC;;IAE1B;IACA,MAAMS,cAAc,GAAIlC,UAAU,CAACE,KAAK,GAAGF,UAAU,CAACG,KAAK,GAAGH,UAAU,CAACI,MAAM,GAAI,MAAM;IACzF6B,KAAK,IAAIE,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEH,cAAc,CAAC,CAAC;;IAErD;IACA,MAAMI,mBAAmB,GAAG;MAC1BC,IAAI,EAAE,GAAG;MACTC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,GAAG;MACVC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE,GAAG;MACXC,KAAK,EAAE;IACT,CAAC;IACDb,KAAK,IAAIK,mBAAmB,CAAC5B,QAAQ,CAAC,IAAI,GAAG;IAE7C,OAAOuB,KAAK,GAAGrB,QAAQ;EACzB,CAAC;EAED,MAAMmC,eAAe,GAAGA,CAAA,KAAMf,cAAc,CAAC,CAAC;;EAE9C;EACA,MAAMgB,iBAAiB,GAAGrE,MAAM,CAAC;IAC/BsE,eAAe,EAAE,CAAC;IAClBC,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,gBAAgB,EAAE,CAAC;IACnBC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAMC,SAAS,GAAIC,QAAQ,IAAK;IAC9B,IAAI,CAAC/D,SAAS,CAACgE,OAAO,EAAE;IAExB,MAAMC,QAAQ,GAAGX,iBAAiB,CAACU,OAAO;IAC1CC,QAAQ,CAACJ,WAAW,GAAG,IAAI;IAE3B,QAAQE,QAAQ;MACd,KAAK,OAAO;QACVE,QAAQ,CAACV,eAAe,GAAG,CAAC;QAC5BU,QAAQ,CAACT,eAAe,GAAG,CAAC;QAC5BS,QAAQ,CAACN,cAAc,GAAG,CAAC;QAC3B;MACF,KAAK,MAAM;QACTM,QAAQ,CAACV,eAAe,GAAG,CAAC;QAC5BU,QAAQ,CAACT,eAAe,GAAGf,IAAI,CAACyB,EAAE,GAAG,CAAC;QACtCD,QAAQ,CAACN,cAAc,GAAG,CAAC;QAC3B;MACF,KAAK,KAAK;QACRM,QAAQ,CAACV,eAAe,GAAGd,IAAI,CAACyB,EAAE,GAAG,CAAC;QACtCD,QAAQ,CAACT,eAAe,GAAG,CAAC;QAC5BS,QAAQ,CAACN,cAAc,GAAG,CAAC;QAC3B;MACF,KAAK,KAAK;MACV;QACEM,QAAQ,CAACV,eAAe,GAAG,GAAG;QAC9BU,QAAQ,CAACT,eAAe,GAAG,GAAG;QAC9BS,QAAQ,CAACN,cAAc,GAAG,CAAC;QAC3B;IACJ;EACF,CAAC;EAED,MAAMQ,UAAU,GAAIC,SAAS,IAAK;IAChC,IAAI,CAACpE,SAAS,CAACgE,OAAO,IAAI,CAACV,iBAAiB,CAACU,OAAO,EAAE;IAEtD,MAAMC,QAAQ,GAAGX,iBAAiB,CAACU,OAAO;IAC1C,MAAMK,UAAU,GAAGD,SAAS,GAAG,GAAG;IAClC,MAAME,WAAW,GAAGL,QAAQ,CAACN,cAAc,GAAGU,UAAU;;IAExD;IACAJ,QAAQ,CAACN,cAAc,GAAGlB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE2B,WAAW,CAAC,CAAC;;IAElE;IACAL,QAAQ,CAACJ,WAAW,GAAG,IAAI;IAC3BU,UAAU,CAAC,MAAM;MACf,IAAIN,QAAQ,EAAEA,QAAQ,CAACJ,WAAW,GAAG,KAAK;IAC5C,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMW,mBAAmB,GAAIC,KAAK,IAAK;IACrC;IACA,MAAMC,WAAW,GAAG,IAAIvF,KAAK,CAACwF,WAAW,CACvCrE,UAAU,CAACE,KAAK,GAAG,GAAG,EACtB,IAAI,EACJF,UAAU,CAACG,KAAK,GAAG,GACrB,CAAC;IACD,MAAMmE,WAAW,GAAG,IAAIzF,KAAK,CAAC0F,oBAAoB,CAAC;MACjDC,KAAK,EAAEnE,MAAM,CAACE,OAAO;MACrBkE,SAAS,EAAE/D,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;MAC3CgE,SAAS,EAAEhE,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;MAC3CiE,WAAW,EAAEjE,QAAQ,KAAK,OAAO;MACjCkE,OAAO,EAAElE,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG;IACxC,CAAC,CAAC;IAEF,MAAMmE,QAAQ,GAAG,IAAIhG,KAAK,CAACiG,IAAI,CAACV,WAAW,EAAEE,WAAW,CAAC;IACzDO,QAAQ,CAACE,QAAQ,CAACC,CAAC,GAAGhF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI;IACpD,IAAI,CAACU,QAAQ,EAAE;MACb+D,QAAQ,CAACI,UAAU,GAAG,IAAI;MAC1BJ,QAAQ,CAACK,aAAa,GAAG,IAAI;IAC/B;IACAf,KAAK,CAACgB,GAAG,CAACN,QAAQ,CAAC;;IAEnB;IACA,MAAMO,WAAW,GAAGtE,QAAQ,GAAG,CAAC,GAAG,EAAE;IACrC,MAAMuE,WAAW,GAAG,IAAIxG,KAAK,CAACyG,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAEtF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,EAAEgF,WAAW,CAAC;IACvG,MAAMG,WAAW,GAAG,IAAI1G,KAAK,CAAC0F,oBAAoB,CAAC;MACjDC,KAAK,EAAEnE,MAAM,CAACE,OAAO;MACrBkE,SAAS,EAAE,GAAG;MACdC,SAAS,EAAEhE,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG;IAC1C,CAAC,CAAC;IAEF,MAAM8E,YAAY,GAAG,CACnB,CAAC,CAACxF,UAAU,CAACE,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,EAAE,CAACJ,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,EACpG,CAACH,UAAU,CAACE,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,EAAE,CAACJ,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,EACnG,CAAC,CAACH,UAAU,CAACE,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,EAAEJ,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,EACnG,CAACH,UAAU,CAACE,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,EAAEJ,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,CACnG;IAEDqF,YAAY,CAACC,OAAO,CAACC,GAAG,IAAI;MAC1B,MAAMC,GAAG,GAAG,IAAI9G,KAAK,CAACiG,IAAI,CAACO,WAAW,EAAEE,WAAW,CAAC;MACpDI,GAAG,CAACZ,QAAQ,CAACa,GAAG,CAACF,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC;MACxC,IAAI,CAAC5E,QAAQ,EAAE;QACb6E,GAAG,CAACV,UAAU,GAAG,IAAI;MACvB;MACAd,KAAK,CAACgB,GAAG,CAACQ,GAAG,CAAC;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,mBAAmB,GAAI1B,KAAK,IAAK;IACrC;IACA,MAAM2B,YAAY,GAAG,IAAIjH,KAAK,CAACwF,WAAW,CACxCrE,UAAU,CAACE,KAAK,GAAG,GAAG,EACtB,IAAI,EACJF,UAAU,CAACG,KAAK,GAAG,GACrB,CAAC;IACD,MAAM4F,YAAY,GAAG,IAAIlH,KAAK,CAAC0F,oBAAoB,CAAC;MAClDC,KAAK,EAAEnE,MAAM,CAACE,OAAO;MACrBkE,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAMuB,IAAI,GAAG,IAAInH,KAAK,CAACiG,IAAI,CAACgB,YAAY,EAAEC,YAAY,CAAC;IACvDC,IAAI,CAACjB,QAAQ,CAACC,CAAC,GAAG,GAAG;IACrB,IAAI,CAAClE,QAAQ,EAAE;MACbkF,IAAI,CAACf,UAAU,GAAG,IAAI;IACxB;IACAd,KAAK,CAACgB,GAAG,CAACa,IAAI,CAAC;;IAEf;IACA,MAAMC,YAAY,GAAG,IAAIpH,KAAK,CAACwF,WAAW,CACxCrE,UAAU,CAACE,KAAK,GAAG,GAAG,EACtB,IAAI,EACJF,UAAU,CAACI,MAAM,GAAG,GACtB,CAAC;IACD,MAAM8F,YAAY,GAAG,IAAIrH,KAAK,CAAC0F,oBAAoB,CAAC;MAClDC,KAAK,EAAEnE,MAAM,CAACE,OAAO;MACrBkE,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAM0B,QAAQ,GAAG,IAAItH,KAAK,CAACiG,IAAI,CAACmB,YAAY,EAAEC,YAAY,CAAC;IAC3DC,QAAQ,CAACpB,QAAQ,CAACa,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC5F,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC;IAC7D,IAAI,CAACW,QAAQ,EAAE;MACbqF,QAAQ,CAAClB,UAAU,GAAG,IAAI;IAC5B;IACAd,KAAK,CAACgB,GAAG,CAACgB,QAAQ,CAAC;;IAEnB;IACA,MAAMC,YAAY,GAAGtF,QAAQ,GAAG,CAAC,GAAG,CAAC;IACrC,MAAMuF,YAAY,GAAG,IAAIxH,KAAK,CAACyG,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAEc,YAAY,CAAC;IAC7E,MAAME,YAAY,GAAG,IAAIzH,KAAK,CAAC0F,oBAAoB,CAAC;MAClDC,KAAK,EAAEnE,MAAM,CAACE,OAAO;MACrBkE,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAM8B,IAAI,GAAG,IAAI1H,KAAK,CAACiG,IAAI,CAACuB,YAAY,EAAEC,YAAY,CAAC;IACvDC,IAAI,CAACxB,QAAQ,CAACC,CAAC,GAAG,IAAI;IACtB,IAAI,CAAClE,QAAQ,EAAE;MACbyF,IAAI,CAACtB,UAAU,GAAG,IAAI;IACxB;IACAd,KAAK,CAACgB,GAAG,CAACoB,IAAI,CAAC;EACjB,CAAC;EAED,MAAMC,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,YAAY,GAAG,IAAI7H,KAAK,CAAC8H,KAAK,CAAC,CAAC;IAEtC,QAAQ5G,WAAW;MACjB,KAAK,OAAO;QACVmE,mBAAmB,CAACwC,YAAY,CAAC;QACjC;MACF,KAAK,OAAO;QACVb,mBAAmB,CAACa,YAAY,CAAC;QACjC;MACF;QACExC,mBAAmB,CAACwC,YAAY,CAAC;IACrC;IAEAlH,UAAU,CAACkE,OAAO,GAAGgD,YAAY;IACjCD,KAAK,CAACtB,GAAG,CAACuB,YAAY,CAAC;EACzB,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACpH,UAAU,CAACkE,OAAO,IAAI,CAACpE,QAAQ,CAACoE,OAAO,EAAE;;IAE9C;IACApE,QAAQ,CAACoE,OAAO,CAACmD,MAAM,CAACrH,UAAU,CAACkE,OAAO,CAAC;;IAE3C;IACA8C,aAAa,CAAClH,QAAQ,CAACoE,OAAO,CAAC;EACjC,CAAC;;EAED;EACA9E,SAAS,CAAC,MAAM;IACd,IAAI,CAACS,QAAQ,CAACqE,OAAO,EAAE;;IAEvB;IACA,MAAM+C,KAAK,GAAG,IAAI5H,KAAK,CAACiI,KAAK,CAAC,CAAC;IAC/BL,KAAK,CAACM,UAAU,GAAG,IAAIlI,KAAK,CAACmI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC9C1H,QAAQ,CAACoE,OAAO,GAAG+C,KAAK;;IAExB;IACA,MAAMQ,MAAM,GAAG,IAAIpI,KAAK,CAACqI,iBAAiB,CACxC,EAAE,EACF7H,QAAQ,CAACqE,OAAO,CAACyD,WAAW,GAAG9H,QAAQ,CAACqE,OAAO,CAAC0D,YAAY,EAC5D,GAAG,EACH,IACF,CAAC;IACDH,MAAM,CAAClC,QAAQ,CAACa,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BqB,MAAM,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB3H,SAAS,CAACgE,OAAO,GAAGuD,MAAM;;IAE1B;IACA,MAAMK,QAAQ,GAAG,IAAIzI,KAAK,CAAC0I,aAAa,CAAC;MACvCC,SAAS,EAAE,CAAC1G,QAAQ;MACpB2G,KAAK,EAAE,IAAI;MACXC,eAAe,EAAE5G,QAAQ,GAAG,WAAW,GAAG;IAC5C,CAAC,CAAC;IACFwG,QAAQ,CAACK,OAAO,CAACtI,QAAQ,CAACqE,OAAO,CAACyD,WAAW,EAAE9H,QAAQ,CAACqE,OAAO,CAAC0D,YAAY,CAAC;IAC7EE,QAAQ,CAACM,aAAa,CAACzF,IAAI,CAACE,GAAG,CAACnB,MAAM,CAAC2G,gBAAgB,EAAE/G,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;IAC7EwG,QAAQ,CAACQ,aAAa,CAAC,QAAQ,EAAE,GAAG,CAAC;IACrCR,QAAQ,CAACS,SAAS,CAACC,OAAO,GAAG,CAAClH,QAAQ;IACtC,IAAI,CAACA,QAAQ,EAAE;MACbwG,QAAQ,CAACS,SAAS,CAACE,IAAI,GAAGpJ,KAAK,CAACqJ,gBAAgB;IAClD;IACA7I,QAAQ,CAACqE,OAAO,CAACyE,WAAW,CAACb,QAAQ,CAACc,UAAU,CAAC;IACjD7I,WAAW,CAACmE,OAAO,GAAG4D,QAAQ;;IAE9B;IACA,MAAMe,YAAY,GAAG,IAAIxJ,KAAK,CAACyJ,YAAY,CAAC,QAAQ,EAAExH,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;IAC3E2F,KAAK,CAACtB,GAAG,CAACkD,YAAY,CAAC;IAEvB,MAAME,gBAAgB,GAAG,IAAI1J,KAAK,CAAC2J,gBAAgB,CAAC,QAAQ,EAAE1H,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;IACnFyH,gBAAgB,CAACxD,QAAQ,CAACa,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtC,IAAI,CAAC9E,QAAQ,EAAE;MACbyH,gBAAgB,CAACtD,UAAU,GAAG,IAAI;MAClCsD,gBAAgB,CAACE,MAAM,CAACC,OAAO,CAACxI,KAAK,GAAG,IAAI;MAC5CqI,gBAAgB,CAACE,MAAM,CAACC,OAAO,CAACtI,MAAM,GAAG,IAAI;IAC/C;IACAqG,KAAK,CAACtB,GAAG,CAACoD,gBAAgB,CAAC;;IAE3B;IACA,IAAII,WAAW,GAAG,KAAK;IACvB,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIC,MAAM,GAAG,CAAC;;IAEd;IACA,MAAMlF,QAAQ,GAAGX,iBAAiB,CAACU,OAAO;;IAE1C;IACA,MAAMoF,WAAW,GAAIC,KAAK,IAAK;MAC7BJ,WAAW,GAAG,IAAI;MAClBC,MAAM,GAAGG,KAAK,CAACC,OAAO;MACtBH,MAAM,GAAGE,KAAK,CAACE,OAAO;IACxB,CAAC;IAED,MAAMC,WAAW,GAAIH,KAAK,IAAK;MAC7B,IAAI,CAACJ,WAAW,EAAE;MAClB,MAAMQ,MAAM,GAAGJ,KAAK,CAACC,OAAO,GAAGJ,MAAM;MACrC,MAAMQ,MAAM,GAAGL,KAAK,CAACE,OAAO,GAAGJ,MAAM;;MAErC;MACAlF,QAAQ,CAACT,eAAe,IAAIiG,MAAM,GAAG,KAAK;MAC1CxF,QAAQ,CAACV,eAAe,IAAImG,MAAM,GAAG,KAAK;MAE1CzF,QAAQ,CAACV,eAAe,GAAGd,IAAI,CAACC,GAAG,CAAC,CAACD,IAAI,CAACyB,EAAE,GAAG,CAAC,EAAEzB,IAAI,CAACE,GAAG,CAACF,IAAI,CAACyB,EAAE,GAAG,CAAC,EAAED,QAAQ,CAACV,eAAe,CAAC,CAAC;MAElG2F,MAAM,GAAGG,KAAK,CAACC,OAAO;MACtBH,MAAM,GAAGE,KAAK,CAACE,OAAO;IACxB,CAAC;IAED,MAAMI,SAAS,GAAGA,CAAA,KAAM;MACtBV,WAAW,GAAG,KAAK;IACrB,CAAC;IAED,MAAMW,OAAO,GAAIP,KAAK,IAAK;MACzBA,KAAK,CAACQ,cAAc,CAAC,CAAC;MACtB,MAAMC,SAAS,GAAG1I,QAAQ,GAAG,KAAK,GAAG,IAAI;MACzC6C,QAAQ,CAACN,cAAc,IAAI0F,KAAK,CAACK,MAAM,GAAGI,SAAS;MACnD7F,QAAQ,CAACN,cAAc,GAAGlB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEsB,QAAQ,CAACN,cAAc,CAAC,CAAC;IAChF,CAAC;;IAED;IACA,IAAIoG,iBAAiB,GAAG,CAAC;IACzB,IAAIC,oBAAoB,GAAG,CAAC;IAE5B,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;MACpC,IAAIA,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;MAChC,MAAMC,EAAE,GAAGF,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO,GAAGY,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO;MAClD,MAAMe,EAAE,GAAGH,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO,GAAGW,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO;MAClD,OAAO9G,IAAI,CAAC6H,IAAI,CAACF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;IACrC,CAAC;IAED,MAAME,YAAY,GAAIlB,KAAK,IAAK;MAC9B,IAAIA,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC9BlB,WAAW,GAAG,IAAI;QAClBC,MAAM,GAAGG,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO;QACjCH,MAAM,GAAGE,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO;MACnC,CAAC,MAAM,IAAIF,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACrClB,WAAW,GAAG,KAAK;QACnBe,oBAAoB,GAAGC,gBAAgB,CAACZ,KAAK,CAACa,OAAO,CAAC;QACtDH,iBAAiB,GAAGC,oBAAoB;MAC1C;IACF,CAAC;IAED,MAAMQ,WAAW,GAAInB,KAAK,IAAK;MAC7BA,KAAK,CAACQ,cAAc,CAAC,CAAC;MAEtB,IAAIR,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,IAAIlB,WAAW,EAAE;QAC7C;QACA,MAAMQ,MAAM,GAAGJ,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO,GAAGJ,MAAM;QAChD,MAAMQ,MAAM,GAAGL,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO,GAAGJ,MAAM;QAEhDlF,QAAQ,CAACT,eAAe,IAAIiG,MAAM,GAAG,KAAK;QAC1CxF,QAAQ,CAACV,eAAe,IAAImG,MAAM,GAAG,KAAK;QAE1CzF,QAAQ,CAACV,eAAe,GAAGd,IAAI,CAACC,GAAG,CAAC,CAACD,IAAI,CAACyB,EAAE,GAAG,CAAC,EAAEzB,IAAI,CAACE,GAAG,CAACF,IAAI,CAACyB,EAAE,GAAG,CAAC,EAAED,QAAQ,CAACV,eAAe,CAAC,CAAC;QAElG2F,MAAM,GAAGG,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO;QACjCH,MAAM,GAAGE,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO;MACnC,CAAC,MAAM,IAAIF,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACrC;QACA,MAAMvG,eAAe,GAAGqG,gBAAgB,CAACZ,KAAK,CAACa,OAAO,CAAC;QACvD,MAAMO,aAAa,GAAG7G,eAAe,GAAGmG,iBAAiB;QAEzD,IAAItH,IAAI,CAACiI,GAAG,CAACD,aAAa,CAAC,GAAG,CAAC,EAAE;UAAE;UACjCxG,QAAQ,CAACN,cAAc,IAAI8G,aAAa,GAAG,KAAK;UAChDxG,QAAQ,CAACN,cAAc,GAAGlB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEsB,QAAQ,CAACN,cAAc,CAAC,CAAC;UAC9EoG,iBAAiB,GAAGnG,eAAe;QACrC;MACF;IACF,CAAC;IAED,MAAM+G,UAAU,GAAItB,KAAK,IAAK;MAC5B,IAAIA,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC9BlB,WAAW,GAAG,KAAK;MACrB,CAAC,MAAM,IAAII,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACrClB,WAAW,GAAG,IAAI;QAClBC,MAAM,GAAGG,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO;QACjCH,MAAM,GAAGE,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO;MACnC;IACF,CAAC;;IAED;IACA,MAAMqB,MAAM,GAAGhD,QAAQ,CAACc,UAAU;;IAElC;IACAkC,MAAM,CAAC/I,gBAAgB,CAAC,WAAW,EAAEuH,WAAW,CAAC;IACjDyB,QAAQ,CAAChJ,gBAAgB,CAAC,WAAW,EAAE2H,WAAW,CAAC,CAAC,CAAC;IACrDqB,QAAQ,CAAChJ,gBAAgB,CAAC,SAAS,EAAE8H,SAAS,CAAC,CAAC,CAAC;IACjDiB,MAAM,CAAC/I,gBAAgB,CAAC,OAAO,EAAE+H,OAAO,EAAE;MAAEkB,OAAO,EAAE;IAAM,CAAC,CAAC;;IAE7D;IACAF,MAAM,CAAC/I,gBAAgB,CAAC,YAAY,EAAE0I,YAAY,EAAE;MAAEO,OAAO,EAAE;IAAM,CAAC,CAAC;IACvEF,MAAM,CAAC/I,gBAAgB,CAAC,WAAW,EAAE2I,WAAW,EAAE;MAAEM,OAAO,EAAE;IAAM,CAAC,CAAC;IACrEF,MAAM,CAAC/I,gBAAgB,CAAC,UAAU,EAAE8I,UAAU,EAAE;MAAEG,OAAO,EAAE;IAAM,CAAC,CAAC;;IAEnE;IACAF,MAAM,CAAC/I,gBAAgB,CAAC,aAAa,EAAGkJ,CAAC,IAAKA,CAAC,CAAClB,cAAc,CAAC,CAAC,CAAC;;IAEjE;IACAe,MAAM,CAACI,KAAK,CAACC,WAAW,GAAG,MAAM;IACjCL,MAAM,CAACI,KAAK,CAACE,UAAU,GAAG,MAAM;;IAEhC;IACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,UAAU,GAAGnH,QAAQ,CAACJ,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;;MAEvD;MACAI,QAAQ,CAACR,gBAAgB,IAAI,CAACQ,QAAQ,CAACV,eAAe,GAAGU,QAAQ,CAACR,gBAAgB,IAAI2H,UAAU;MAChGnH,QAAQ,CAACP,gBAAgB,IAAI,CAACO,QAAQ,CAACT,eAAe,GAAGS,QAAQ,CAACP,gBAAgB,IAAI0H,UAAU;MAChGnH,QAAQ,CAACL,eAAe,IAAI,CAACK,QAAQ,CAACN,cAAc,GAAGM,QAAQ,CAACL,eAAe,IAAIwH,UAAU;;MAE7F;MACA,MAAMC,CAAC,GAAG5I,IAAI,CAAC6I,GAAG,CAACrH,QAAQ,CAACP,gBAAgB,CAAC,GAAGjB,IAAI,CAAC8I,GAAG,CAACtH,QAAQ,CAACR,gBAAgB,CAAC,GAAGQ,QAAQ,CAACL,eAAe;MAC9G,MAAM0B,CAAC,GAAG7C,IAAI,CAAC6I,GAAG,CAACrH,QAAQ,CAACR,gBAAgB,CAAC,GAAGQ,QAAQ,CAACL,eAAe,GAAG,CAAC;MAC5E,MAAM4H,CAAC,GAAG/I,IAAI,CAAC8I,GAAG,CAACtH,QAAQ,CAACP,gBAAgB,CAAC,GAAGjB,IAAI,CAAC8I,GAAG,CAACtH,QAAQ,CAACR,gBAAgB,CAAC,GAAGQ,QAAQ,CAACL,eAAe;MAE9G2D,MAAM,CAAClC,QAAQ,CAACa,GAAG,CAACmF,CAAC,EAAE/F,CAAC,EAAEkG,CAAC,CAAC;MAC5BjE,MAAM,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACA,IAAI1D,QAAQ,CAACJ,WAAW,EAAE;QACxB,MAAM4H,QAAQ,GAAGhJ,IAAI,CAACiI,GAAG,CAACzG,QAAQ,CAACV,eAAe,GAAGU,QAAQ,CAACR,gBAAgB,CAAC;QAC/E,MAAMiI,QAAQ,GAAGjJ,IAAI,CAACiI,GAAG,CAACzG,QAAQ,CAACT,eAAe,GAAGS,QAAQ,CAACP,gBAAgB,CAAC;QAC/E,MAAMiI,QAAQ,GAAGlJ,IAAI,CAACiI,GAAG,CAACzG,QAAQ,CAACN,cAAc,GAAGM,QAAQ,CAACL,eAAe,CAAC;QAE7E,IAAI6H,QAAQ,GAAG,IAAI,IAAIC,QAAQ,GAAG,IAAI,IAAIC,QAAQ,GAAG,GAAG,EAAE;UACxD1H,QAAQ,CAACJ,WAAW,GAAG,KAAK;QAC9B;MACF;IACF,CAAC;;IAED;IACAI,QAAQ,CAACV,eAAe,GAAG,GAAG;IAC9BU,QAAQ,CAACT,eAAe,GAAG,GAAG;IAC9BS,QAAQ,CAACN,cAAc,GAAG,CAAC;IAC3BM,QAAQ,CAACR,gBAAgB,GAAG,GAAG;IAC/BQ,QAAQ,CAACP,gBAAgB,GAAG,GAAG;IAC/BO,QAAQ,CAACL,eAAe,GAAG,CAAC;;IAE5B;IACAkD,aAAa,CAACC,KAAK,CAAC;;IAEpB;IACA,MAAM6E,OAAO,GAAGA,CAAA,KAAM;MACpB7L,cAAc,CAACiE,OAAO,GAAG6H,qBAAqB,CAACD,OAAO,CAAC;MACvDT,YAAY,CAAC,CAAC;MACdvD,QAAQ,CAACkE,MAAM,CAAC/E,KAAK,EAAEQ,MAAM,CAAC;IAChC,CAAC;IACDqE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAACpM,QAAQ,CAACqE,OAAO,EAAE;MACvB,MAAMxD,KAAK,GAAGb,QAAQ,CAACqE,OAAO,CAACyD,WAAW;MAC1C,MAAM/G,MAAM,GAAGf,QAAQ,CAACqE,OAAO,CAAC0D,YAAY;MAC5CH,MAAM,CAACyE,MAAM,GAAGxL,KAAK,GAAGE,MAAM;MAC9B6G,MAAM,CAAC0E,sBAAsB,CAAC,CAAC;MAC/BrE,QAAQ,CAACK,OAAO,CAACzH,KAAK,EAAEE,MAAM,CAAC;IACjC,CAAC;IAEDc,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAEkK,YAAY,CAAC;IAE/C,OAAO,MAAM;MACXvK,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAEiK,YAAY,CAAC;;MAElD;MACA,MAAMnB,MAAM,GAAGhD,QAAQ,CAACc,UAAU;MAClCkC,MAAM,CAAC9I,mBAAmB,CAAC,WAAW,EAAEsH,WAAW,CAAC;MACpDwB,MAAM,CAAC9I,mBAAmB,CAAC,OAAO,EAAE8H,OAAO,CAAC;MAC5CgB,MAAM,CAAC9I,mBAAmB,CAAC,YAAY,EAAEyI,YAAY,CAAC;MACtDK,MAAM,CAAC9I,mBAAmB,CAAC,WAAW,EAAE0I,WAAW,CAAC;MACpDI,MAAM,CAAC9I,mBAAmB,CAAC,UAAU,EAAE6I,UAAU,CAAC;MAClDC,MAAM,CAAC9I,mBAAmB,CAAC,aAAa,EAAGiJ,CAAC,IAAKA,CAAC,CAAClB,cAAc,CAAC,CAAC,CAAC;;MAEpE;MACAgB,QAAQ,CAAC/I,mBAAmB,CAAC,WAAW,EAAE0H,WAAW,CAAC;MACtDqB,QAAQ,CAAC/I,mBAAmB,CAAC,SAAS,EAAE6H,SAAS,CAAC;MAElD,IAAI5J,cAAc,CAACiE,OAAO,EAAE;QAC1BkI,oBAAoB,CAACnM,cAAc,CAACiE,OAAO,CAAC;MAC9C;MACA,IAAIrE,QAAQ,CAACqE,OAAO,IAAI4D,QAAQ,CAACc,UAAU,EAAE;QAC3C/I,QAAQ,CAACqE,OAAO,CAACmI,WAAW,CAACvE,QAAQ,CAACc,UAAU,CAAC;MACnD;MACAd,QAAQ,CAACwE,OAAO,CAAC,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,CAAChL,QAAQ,EAAEd,UAAU,EAAEK,MAAM,EAAEK,QAAQ,EAAEX,WAAW,CAAC,CAAC;;EAEzD;EACAnB,SAAS,CAAC,MAAM;IACd,IAAIU,QAAQ,CAACoE,OAAO,IAAIlE,UAAU,CAACkE,OAAO,EAAE;MAC1CkD,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAAC5G,UAAU,EAAEK,MAAM,EAAEK,QAAQ,CAAC,CAAC;EAElC,MAAMqL,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,aAAa,GAAG;MACpBjM,WAAW;MACXC,UAAU;MACVK,MAAM;MACNK,QAAQ;MACRE,QAAQ;MACRqB,KAAK,EAAEc,eAAe,CAAC;IACzB,CAAC;IAEDkJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,aAAa,CAAC;IAC7CG,KAAK,CAAC,SAASvL,QAAQ,IAAIb,WAAW,oBAAoBgD,eAAe,CAAC,CAAC,CAACqJ,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAC3F,CAAC;EAED,oBACEpN,OAAA;IAAKqN,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBAErCtN,OAAA;MAAKqN,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClCtN,OAAA;QAAKqN,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtN,OAAA;UAAQuN,OAAO,EAAErN,MAAO;UAACmN,SAAS,EAAC,UAAU;UAAAC,QAAA,gBAC3CtN,OAAA;YAAKkB,KAAK,EAAC,IAAI;YAACE,MAAM,EAAC,IAAI;YAACoM,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAAH,QAAA,eACzDtN,OAAA;cAAM0N,CAAC,EAAC,yBAAyB;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC,oBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlO,OAAA;UAAAsN,QAAA,GAAI,WAAS,EAACvM,WAAW,CAACoN,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGrN,WAAW,CAACsN,KAAK,CAAC,CAAC,CAAC,EAAC,eAAa;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlO,OAAA;MAAKqN,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCtN,OAAA;QAAKqN,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBtN,OAAA;UAAKqN,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAE7CtN,OAAA;YAAKqN,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BtN,OAAA;cAAKqN,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCtN,OAAA;gBAAKqN,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BtN,OAAA;kBAAKqN,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxBtN,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACoM,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDtN,OAAA;sBAAM0N,CAAC,EAAC,4BAA4B;sBAACD,IAAI,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACrDlO,OAAA;sBAAM0N,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAC9DlO,OAAA;sBAAM0N,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAKqN,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBtN,OAAA;oBAAAsN,QAAA,EAAI;kBAAU;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBlO,OAAA;oBAAAsN,QAAA,GAAG,uCAAqC,EAACvM,WAAW;kBAAA;oBAAAgN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlO,OAAA;gBAAKqN,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCtN,OAAA;kBAAKqN,SAAS,EAAC,cAAc;kBAACiB,GAAG,EAAEjO;gBAAS;kBAAA0N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDlO,OAAA;kBAAKqN,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClCtN,OAAA;oBAAKqN,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtCtN,OAAA;sBACEqN,SAAS,EAAC,gBAAgB;sBAC1BE,OAAO,EAAEA,CAAA,KAAM/I,SAAS,CAAC,OAAO,CAAE;sBAClC+J,KAAK,EAAC,YAAY;sBAAAjB,QAAA,gBAElBtN,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACoM,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDtN,OAAA;0BAAM+L,CAAC,EAAC,GAAG;0BAAC/F,CAAC,EAAC,GAAG;0BAAC9E,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACuM,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC,MAAM;0BAACe,EAAE,EAAC;wBAAG;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACrGlO,OAAA;0BAAQyO,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,KAAK;0BAAClB,IAAI,EAAC;wBAAc;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC,eACNlO,OAAA;wBAAAsN,QAAA,EAAM;sBAAK;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC,eACTlO,OAAA;sBACEqN,SAAS,EAAC,gBAAgB;sBAC1BE,OAAO,EAAEA,CAAA,KAAM/I,SAAS,CAAC,MAAM,CAAE;sBACjC+J,KAAK,EAAC,WAAW;sBAAAjB,QAAA,gBAEjBtN,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACoM,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDtN,OAAA;0BAAM0N,CAAC,EAAC,kBAAkB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACpElO,OAAA;0BAAQyO,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,KAAK;0BAAClB,IAAI,EAAC;wBAAc;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC,eACNlO,OAAA;wBAAAsN,QAAA,EAAM;sBAAI;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACTlO,OAAA;sBACEqN,SAAS,EAAC,gBAAgB;sBAC1BE,OAAO,EAAEA,CAAA,KAAM/I,SAAS,CAAC,KAAK,CAAE;sBAChC+J,KAAK,EAAC,UAAU;sBAAAjB,QAAA,gBAEhBtN,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACoM,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDtN,OAAA;0BAAM0N,CAAC,EAAC,4BAA4B;0BAACD,IAAI,EAAC;wBAAc;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAC1DlO,OAAA;0BAAM0N,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E,CAAC,eACNlO,OAAA;wBAAAsN,QAAA,EAAM;sBAAG;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACTlO,OAAA;sBACEqN,SAAS,EAAC,gBAAgB;sBAC1BE,OAAO,EAAEA,CAAA,KAAM/I,SAAS,CAAC,KAAK,CAAE;sBAChC+J,KAAK,EAAC,SAAS;sBAAAjB,QAAA,gBAEftN,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACoM,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDtN,OAAA;0BAAM0N,CAAC,EAAC,4BAA4B;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAC1FlO,OAAA;0BAAM0N,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACjFlO,OAAA;0BAAM0N,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E,CAAC,eACNlO,OAAA;wBAAAsN,QAAA,EAAM;sBAAE;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNlO,OAAA;oBAAKqN,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,gBACvCtN,OAAA;sBACEqN,SAAS,EAAC,cAAc;sBACxBE,OAAO,EAAEA,CAAA,KAAM1I,UAAU,CAAC,CAAC,CAAC,CAAE;sBAC9B0J,KAAK,EAAC,UAAU;sBAAAjB,QAAA,eAEhBtN,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACoM,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDtN,OAAA;0BAAQyO,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,GAAG;0BAAChB,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACvElO,OAAA;0BAAM0N,CAAC,EAAC,SAAS;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAC3DlO,OAAA;0BAAM0N,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACTlO,OAAA;sBACEqN,SAAS,EAAC,cAAc;sBACxBE,OAAO,EAAEA,CAAA,KAAM1I,UAAU,CAAC,CAAC,CAAE;sBAC7B0J,KAAK,EAAC,SAAS;sBAAAjB,QAAA,eAEftN,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACoM,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDtN,OAAA;0BAAQyO,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,GAAG;0BAAChB,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACvElO,OAAA;0BAAM0N,CAAC,EAAC,gBAAgB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAClElO,OAAA;0BAAM0N,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAKqN,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,eAClCtN,OAAA;oBAAAsN,QAAA,EACGxL,QAAQ,GACL,mDAAmD,GACnD;kBAAoD;oBAAAiM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEpD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlO,OAAA;cAAKqN,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCtN,OAAA;gBAAAsN,QAAA,EAAK,CAAAnN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAES,IAAI,KAAI,UAAUG,WAAW,CAACoN,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGrN,WAAW,CAACsN,KAAK,CAAC,CAAC,CAAC;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClGlO,OAAA;gBAAGqN,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,GAAC,yBACV,EAACvM,WAAW,EAAC,mHAEtC;cAAA;gBAAAgN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJlO,OAAA;gBAAKqN,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BtN,OAAA;kBAAKqN,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BtN,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACoM,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDtN,OAAA;sBAAM0N,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvGlO,OAAA;sBAAQyO,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNlO,OAAA;oBAAAsN,QAAA,EAAM;kBAA0B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACNlO,OAAA;kBAAKqN,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BtN,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACoM,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDtN,OAAA;sBAAM0N,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvGlO,OAAA;sBAAQyO,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNlO,OAAA;oBAAAsN,QAAA,EAAM;kBAAiB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNlO,OAAA;kBAAKqN,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BtN,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACoM,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDtN,OAAA;sBAAM0N,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvGlO,OAAA;sBAAQyO,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNlO,OAAA;oBAAAsN,QAAA,EAAM;kBAAiB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNlO,OAAA;kBAAKqN,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BtN,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACoM,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDtN,OAAA;sBAAM0N,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvGlO,OAAA;sBAAQyO,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNlO,OAAA;oBAAAsN,QAAA,EAAM;kBAAoB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlO,OAAA;YAAKqN,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAE3BtN,OAAA;cAAKqN,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BtN,OAAA;gBAAKqN,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BtN,OAAA;kBAAKqN,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxBtN,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACoM,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDtN,OAAA;sBAAM0N,CAAC,EAAC,gCAAgC;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACjGlO,OAAA;sBAAM0N,CAAC,EAAC,qDAAqD;sBAACD,IAAI,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAKqN,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBtN,OAAA;oBAAAsN,QAAA,EAAI;kBAAU;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBlO,OAAA;oBAAAsN,QAAA,EAAG;kBAA6B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlO,OAAA;gBAAKqN,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CtN,OAAA;kBAAKqN,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCtN,OAAA;oBAAKqN,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BtN,OAAA;sBAAOqN,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAK;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChDlO,OAAA;sBAAKqN,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,gBACvCtN,OAAA;wBACEqN,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMtM,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEE,KAAK,EAAEiC,IAAI,CAACC,GAAG,CAACrC,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG,EAAEC,UAAU,CAACE,KAAK,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAAoM,QAAA,eAE1HtN,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACoM,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDtN,OAAA;4BAAM0N,CAAC,EAAC,UAAU;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACTlO,OAAA;wBAAKqN,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChCtN,OAAA;0BAAMqN,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,EAAEtM,UAAU,CAACE;wBAAK;0BAAA6M,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC3DlO,OAAA;0BAAMqN,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,EAAC;wBAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACNlO,OAAA;wBACEqN,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMtM,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEE,KAAK,EAAEiC,IAAI,CAACE,GAAG,CAACtC,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG,EAAEC,UAAU,CAACE,KAAK,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAAoM,QAAA,eAE1HtN,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACoM,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDtN,OAAA;4BAAM0N,CAAC,EAAC,kBAAkB;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBACEiJ,IAAI,EAAC,OAAO;oBACZ5F,GAAG,EAAEtC,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAI;oBACxCqC,GAAG,EAAErC,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAI;oBACxC6N,KAAK,EAAE5N,UAAU,CAACE,KAAM;oBACxB2N,QAAQ,EAAGpD,CAAC,IAAKxK,aAAa,CAAC;sBAAC,GAAGD,UAAU;sBAAEE,KAAK,EAAE4N,QAAQ,CAACrD,CAAC,CAACsD,MAAM,CAACH,KAAK;oBAAC,CAAC,CAAE;oBACjFvB,SAAS,EAAC;kBAA2B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENlO,OAAA;kBAAKqN,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCtN,OAAA;oBAAKqN,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BtN,OAAA;sBAAOqN,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAK;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChDlO,OAAA;sBAAKqN,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,gBACvCtN,OAAA;wBACEqN,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMtM,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEG,KAAK,EAAEgC,IAAI,CAACC,GAAG,CAACrC,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,EAAE,EAAEC,UAAU,CAACG,KAAK,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAAmM,QAAA,eAEzHtN,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACoM,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDtN,OAAA;4BAAM0N,CAAC,EAAC,UAAU;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACTlO,OAAA;wBAAKqN,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChCtN,OAAA;0BAAMqN,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,EAAEtM,UAAU,CAACG;wBAAK;0BAAA4M,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC3DlO,OAAA;0BAAMqN,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,EAAC;wBAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACNlO,OAAA;wBACEqN,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMtM,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEG,KAAK,EAAEgC,IAAI,CAACE,GAAG,CAACtC,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG,EAAEC,UAAU,CAACG,KAAK,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAAmM,QAAA,eAE1HtN,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACoM,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDtN,OAAA;4BAAM0N,CAAC,EAAC,kBAAkB;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBACEiJ,IAAI,EAAC,OAAO;oBACZ5F,GAAG,EAAEtC,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,EAAG;oBACvCqC,GAAG,EAAErC,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAI;oBACxC6N,KAAK,EAAE5N,UAAU,CAACG,KAAM;oBACxB0N,QAAQ,EAAGpD,CAAC,IAAKxK,aAAa,CAAC;sBAAC,GAAGD,UAAU;sBAAEG,KAAK,EAAE2N,QAAQ,CAACrD,CAAC,CAACsD,MAAM,CAACH,KAAK;oBAAC,CAAC,CAAE;oBACjFvB,SAAS,EAAC;kBAA2B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENlO,OAAA;kBAAKqN,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCtN,OAAA;oBAAKqN,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BtN,OAAA;sBAAOqN,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAM;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACjDlO,OAAA;sBAAKqN,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,gBACvCtN,OAAA;wBACEqN,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMtM,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEI,MAAM,EAAE+B,IAAI,CAACC,GAAG,CAACrC,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,EAAE,EAAEC,UAAU,CAACI,MAAM,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAAkM,QAAA,eAE3HtN,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACoM,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDtN,OAAA;4BAAM0N,CAAC,EAAC,UAAU;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACTlO,OAAA;wBAAKqN,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChCtN,OAAA;0BAAMqN,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,EAAEtM,UAAU,CAACI;wBAAM;0BAAA2M,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC5DlO,OAAA;0BAAMqN,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,EAAC;wBAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACNlO,OAAA;wBACEqN,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMtM,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEI,MAAM,EAAE+B,IAAI,CAACE,GAAG,CAACtC,WAAW,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG,EAAEC,UAAU,CAACI,MAAM,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAAkM,QAAA,eAE7HtN,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACoM,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDtN,OAAA;4BAAM0N,CAAC,EAAC,kBAAkB;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBACEiJ,IAAI,EAAC,OAAO;oBACZ5F,GAAG,EAAEtC,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,EAAG;oBACvCqC,GAAG,EAAErC,WAAW,KAAK,OAAO,GAAG,GAAG,GAAG,GAAI;oBACzC6N,KAAK,EAAE5N,UAAU,CAACI,MAAO;oBACzByN,QAAQ,EAAGpD,CAAC,IAAKxK,aAAa,CAAC;sBAAC,GAAGD,UAAU;sBAAEI,MAAM,EAAE0N,QAAQ,CAACrD,CAAC,CAACsD,MAAM,CAACH,KAAK;oBAAC,CAAC,CAAE;oBAClFvB,SAAS,EAAC;kBAA2B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlO,OAAA;cAAKqN,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BtN,OAAA;gBAAKqN,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BtN,OAAA;kBAAKqN,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxBtN,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACoM,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDtN,OAAA;sBAAQyO,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,IAAI;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACjElO,OAAA;sBAAM0N,CAAC,EAAC,0CAA0C;sBAACD,IAAI,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAKqN,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBtN,OAAA;oBAAAsN,QAAA,EAAI;kBAAkB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3BlO,OAAA;oBAAAsN,QAAA,EAAG;kBAAmC;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlO,OAAA;gBAAKqN,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3CtN,OAAA;kBAAKqN,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCtN,OAAA;oBAAKqN,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BtN,OAAA;sBAAAsN,QAAA,EAAI;oBAAa;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtBlO,OAAA;sBAAKqN,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBtN,OAAA;wBAAMqN,SAAS,EAAC,oBAAoB;wBAAAC,QAAA,EACjCjM,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,YAAY,GAC3CF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,WAAW,GAC1CF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,UAAU,GACzCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,OAAO,GACtCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,YAAY,GAC3CF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,QAAQ,GACvCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,eAAe,GAC9CF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,KAAK,GACpCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,OAAO,GACtCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,MAAM,GACrCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,QAAQ,GACvCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,QAAQ,GAAG;sBAAQ;wBAAAwM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,eACPlO,OAAA;wBACEqN,SAAS,EAAC,uBAAuB;wBACjC3B,KAAK,EAAE;0BAAEsD,eAAe,EAAE3N,MAAM,CAACE;wBAAQ;sBAAE;wBAAAwM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAKqN,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCtN,OAAA;sBAAKqN,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BtN,OAAA;wBAAMqN,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnDlO,OAAA;wBAAKqN,SAAS,EAAC,WAAW;wBAAAC,QAAA,EACvB,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC2B,GAAG,CAAEzJ,KAAK,iBAC5ExF,OAAA;0BAEEqN,SAAS,EAAE,yBAAyBhM,MAAM,CAACE,OAAO,KAAKiE,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;0BAC/EkG,KAAK,EAAE;4BAAEsD,eAAe,EAAExJ;0BAAM,CAAE;0BAClC+H,OAAO,EAAEA,CAAA,KAAMjM,SAAS,CAAC;4BAAC,GAAGD,MAAM;4BAAEE,OAAO,EAAEiE;0BAAK,CAAC,CAAE;0BACtD+I,KAAK,EAAE/I,KAAK,KAAK,SAAS,GAAG,YAAY,GAClCA,KAAK,KAAK,SAAS,GAAG,WAAW,GACjCA,KAAK,KAAK,SAAS,GAAG,UAAU,GAChCA,KAAK,KAAK,SAAS,GAAG,OAAO,GAC7BA,KAAK,KAAK,SAAS,GAAG,YAAY,GAAG;wBAAS,GARhDA,KAAK;0BAAAuI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OASX,CACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAKqN,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BtN,OAAA;wBAAMqN,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAa;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxDlO,OAAA;wBAAKqN,SAAS,EAAC,WAAW;wBAAAC,QAAA,EACvB,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC2B,GAAG,CAAEzJ,KAAK,iBAC5ExF,OAAA;0BAEEqN,SAAS,EAAE,yBAAyBhM,MAAM,CAACE,OAAO,KAAKiE,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;0BAC/EkG,KAAK,EAAE;4BAAEsD,eAAe,EAAExJ;0BAAM,CAAE;0BAClC+H,OAAO,EAAEA,CAAA,KAAMjM,SAAS,CAAC;4BAAC,GAAGD,MAAM;4BAAEE,OAAO,EAAEiE;0BAAK,CAAC,CAAE;0BACtD+I,KAAK,EAAE/I,KAAK,KAAK,SAAS,GAAG,eAAe,GACrCA,KAAK,KAAK,SAAS,GAAG,KAAK,GAC3BA,KAAK,KAAK,SAAS,GAAG,OAAO,GAC7BA,KAAK,KAAK,SAAS,GAAG,MAAM,GAC5BA,KAAK,KAAK,SAAS,GAAG,QAAQ,GAAG;wBAAS,GAR5CA,KAAK;0BAAAuI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OASX,CACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlO,OAAA;kBAAKqN,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxCtN,OAAA;oBAAKqN,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BtN,OAAA;sBAAAsN,QAAA,EAAI;oBAAiB;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1BlO,OAAA;sBAAMqN,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpC5L,QAAQ,CAACyM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG1M,QAAQ,CAAC2M,KAAK,CAAC,CAAC;oBAAC;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNlO,OAAA;oBAAKqN,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACvC,CAACvM,WAAW,KAAK,OAAO,GAAG,CAC1B;sBAAEH,IAAI,EAAE,MAAM;sBAAEgO,KAAK,EAAE,MAAM;sBAAEM,IAAI,EAAE,KAAK;sBAAEC,IAAI,EAAE;oBAAyB,CAAC,EAC5E;sBAAEvO,IAAI,EAAE,QAAQ;sBAAEgO,KAAK,EAAE,QAAQ;sBAAEM,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAyB,CAAC,EAC/E;sBAAEvO,IAAI,EAAE,SAAS;sBAAEgO,KAAK,EAAE,SAAS;sBAAEM,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAyB,CAAC,CAClF,GAAG,CACF;sBAAEvO,IAAI,EAAE,MAAM;sBAAEgO,KAAK,EAAE,MAAM;sBAAEM,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAqB,CAAC,EACvE;sBAAEvO,IAAI,EAAE,OAAO;sBAAEgO,KAAK,EAAE,OAAO;sBAAEM,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAuB,CAAC,EAC3E;sBAAEvO,IAAI,EAAE,OAAO;sBAAEgO,KAAK,EAAE,OAAO;sBAAEM,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAyB,CAAC,CAC9E,EAAEF,GAAG,CAAEG,GAAG,iBACTpP,OAAA;sBAEEqN,SAAS,EAAE,4BAA4B3L,QAAQ,KAAK0N,GAAG,CAACR,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;sBAChFrB,OAAO,EAAEA,CAAA,KAAM5L,WAAW,CAACyN,GAAG,CAACR,KAAK,CAAE;sBACtCL,KAAK,EAAEa,GAAG,CAACD,IAAK;sBAAA7B,QAAA,gBAEhBtN,OAAA;wBAAKqN,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAE8B,GAAG,CAACF;sBAAI;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/ClO,OAAA;wBAAKqN,SAAS,EAAC,eAAe;wBAAAC,QAAA,gBAC5BtN,OAAA;0BAAMqN,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAE8B,GAAG,CAACxO;wBAAI;0BAAAmN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACjDlO,OAAA;0BAAMqN,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAE8B,GAAG,CAACD;wBAAI;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA,GATDkB,GAAG,CAACR,KAAK;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAUR,CACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlO,OAAA;cAAKqN,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9CtN,OAAA;gBAAKqN,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCtN,OAAA;kBAAKqN,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/BtN,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACoM,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,eACzDtN,OAAA;sBAAM0N,CAAC,EAAC,+KAA+K;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAKqN,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCtN,OAAA;oBAAAsN,QAAA,EAAI;kBAAa;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBlO,OAAA;oBAAAsN,QAAA,EAAG;kBAAyB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlO,OAAA;gBAAKqN,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBAEnCtN,OAAA;kBAAKqN,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,eACrCtN,OAAA;oBAAKqN,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BtN,OAAA;sBAAKqN,SAAS,EAAC,cAAc;sBAAAC,QAAA,eAC3BtN,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACoM,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDtN,OAAA;0BAAM+L,CAAC,EAAC,GAAG;0BAAC/F,CAAC,EAAC,GAAG;0BAAC9E,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACoN,EAAE,EAAC,GAAG;0BAACb,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAChGlO,OAAA;0BAAM0N,CAAC,EAAC,gBAAgB;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC,KAAK;0BAACC,aAAa,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAKqN,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBAC9BtN,OAAA;wBAAMqN,SAAS,EAAC,cAAc;wBAAAC,QAAA,GAAC,SAAO,EAACvM,WAAW,CAACoN,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGrN,WAAW,CAACsN,KAAK,CAAC,CAAC,CAAC;sBAAA;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACzGlO,OAAA;wBAAMqN,SAAS,EAAC,eAAe;wBAAAC,QAAA,GAAEtM,UAAU,CAACE,KAAK,EAAC,MAAC,EAACF,UAAU,CAACG,KAAK,EAAC,MAAC,EAACH,UAAU,CAACI,MAAM,EAAC,IAAE;sBAAA;wBAAA2M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlO,OAAA;kBAAKqN,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCtN,OAAA;oBAAKqN,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BtN,OAAA;sBAAKkB,KAAK,EAAC,IAAI;sBAACE,MAAM,EAAC,IAAI;sBAACoM,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAH,QAAA,gBACzDtN,OAAA;wBAAM0N,CAAC,EAAC,eAAe;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC,GAAG;wBAACC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eACvGlO,OAAA;wBAAQyO,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,GAAG;wBAAChB,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC,eACNlO,OAAA;sBAAAsN,QAAA,EAAM;oBAAQ;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACNlO,OAAA;oBAAKqN,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,gBACvCtN,OAAA;sBACEuN,OAAO,EAAEA,CAAA,KAAM1L,WAAW,CAACsB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAExB,QAAQ,GAAG,CAAC,CAAC,CAAE;sBACtDyL,SAAS,EAAC,qBAAqB;sBAC/BgC,QAAQ,EAAEzN,QAAQ,IAAI,CAAE;sBAAA0L,QAAA,eAExBtN,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACoM,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,eACzDtN,OAAA;0BAAM0N,CAAC,EAAC,UAAU;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,GAAG;0BAACC,aAAa,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACTlO,OAAA;sBAAKqN,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,eACtCtN,OAAA;wBAAMqN,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,EAAE1L;sBAAQ;wBAAAmM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC,eACNlO,OAAA;sBACEuN,OAAO,EAAEA,CAAA,KAAM1L,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAE;sBACzCyL,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,eAE/BtN,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACoM,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,eACzDtN,OAAA;0BAAM0N,CAAC,EAAC,kBAAkB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,GAAG;0BAACC,aAAa,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlO,OAAA;kBAAKqN,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCtN,OAAA;oBAAKqN,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BtN,OAAA;sBAAKqN,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBACjCtN,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACoM,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDtN,OAAA;0BAAQyO,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,IAAI;0BAAChB,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACnElO,OAAA;0BAAM0N,CAAC,EAAC,aAAa;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC,KAAK;0BAACC,aAAa,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E,CAAC,eACNlO,OAAA;wBAAAsN,QAAA,EAAM;sBAAU;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,eACNlO,OAAA;sBAAMqN,SAAS,EAAC,cAAc;sBAAAC,QAAA,GAAC,GAAC,EAAC7K,YAAY,CAAC,CAAC,CAAC2K,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACNlO,OAAA;oBAAKqN,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BtN,OAAA;sBAAKqN,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBACjCtN,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACoM,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDtN,OAAA;0BAAM0N,CAAC,EAAC,2HAA2H;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACxKlO,OAAA;0BAAUsP,MAAM,EAAC,+BAA+B;0BAAC3B,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACrFlO,OAAA;0BAAMuP,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,OAAO;0BAACC,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAAC/B,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1E,CAAC,eACNlO,OAAA;wBAAAsN,QAAA,EAAM;sBAAe;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACNlO,OAAA;sBAAMqN,SAAS,EAAC,cAAc;sBAAAC,QAAA,GAAC,IAAE,EAAC,CAACvJ,eAAe,CAAC,CAAC,GAAGnC,QAAQ,GAAGa,YAAY,CAAC,CAAC,EAAE2K,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjG,CAAC,eACNlO,OAAA;oBAAKqN,SAAS,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrClO,OAAA;oBAAKqN,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBACzCtN,OAAA;sBAAKqN,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBAC7CtN,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACoM,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDtN,OAAA;0BAAM0N,CAAC,EAAC,2BAA2B;0BAACD,IAAI,EAAC;wBAAS;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACpDlO,OAAA;0BAAM0N,CAAC,EAAC,iBAAiB;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAG;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAC5DlO,OAAA;0BAAM0N,CAAC,EAAC,iBAAiB;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAG;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD,CAAC,eACNlO,OAAA;wBAAAsN,QAAA,GAAM,SAAO,EAAC1L,QAAQ,EAAC,OAAK,EAACA,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,GAAC;sBAAA;wBAAAmM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACNlO,OAAA;sBAAMqN,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,GAAC,GAAC,EAACvJ,eAAe,CAAC,CAAC,CAACqJ,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlO,OAAA;kBAAQqN,SAAS,EAAC,wBAAwB;kBAACE,OAAO,EAAER,eAAgB;kBAAAO,QAAA,eAClEtN,OAAA;oBAAKqN,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBACjCtN,OAAA;sBAAKqN,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,eAC9BtN,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACoM,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDtN,OAAA;0BAAM0N,CAAC,EAAC,uGAAuG;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,GAAG;0BAACC,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC;wBAAO;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACtMlO,OAAA;0BAAQyO,EAAE,EAAC,GAAG;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,GAAG;0BAAChB,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAG;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACpElO,OAAA;0BAAQyO,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,GAAG;0BAAChB,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAG;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAKqN,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBAC9BtN,OAAA;wBAAMqN,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAW;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACtDlO,OAAA;wBAAMqN,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,GAAC,GAAC,EAACvJ,eAAe,CAAC,CAAC,CAACqJ,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9N,EAAA,CA5jCIH,sBAAsB;AAAA0P,EAAA,GAAtB1P,sBAAsB;AA8jC5B,eAAeA,sBAAsB;AAAC,IAAA0P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}