/* Home Page Styles */
.home {
    min-height: 100vh;
}

.hero {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #34495e 100%);
    color: var(--white);
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 500px;
    display: flex;
    align-items: center;
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    background: var(--primary-color);
    color: var(--white);
    padding: 15px 40px;
    border: none;
    border-radius: 5px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-decoration: none;
    display: inline-block;
}

.cta-button:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.hero-navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 30px;
    pointer-events: none;
}

.nav-arrow {
    background: rgba(255,255,255,0.2);
    border: none;
    color: var(--white);
    font-size: 24px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s;
    pointer-events: all;
}

.nav-arrow:hover {
    background: rgba(255,255,255,0.3);
}

.hero-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.4);
    cursor: pointer;
    transition: background-color 0.3s;
}

.indicator.active {
    background: var(--primary-color);
}

.featured-categories,
.featured-products {
    padding: 80px 0;
}

.featured-categories {
    background: var(--background-light);
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background: var(--white);
    text-align: center;
}

.testimonials-header {
    margin-bottom: 60px;
}

.testimonials-label {
    color: #F0B21B;
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 2px;
    text-transform: uppercase;
    margin-bottom: 10px;
    display: block;
}

.testimonials h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin: 15px 0 20px 0;
    font-weight: 600;
}

.testimonials-underline {
    width: 60px;
    height: 4px;
    background: #F0B21B;
    margin: 0 auto;
    border-radius: 2px;
}

.testimonials-slider {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 800px;
    margin: 0 auto 40px auto;
    padding: 0 60px;
}

.testimonial-nav {
    position: absolute;
    background: none;
    border: none;
    font-size: 2rem;
    color: var(--text-light);
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 2;
}

.testimonial-nav:hover {
    color: #F0B21B;
    background: rgba(240, 178, 27, 0.1);
}

.testimonial-nav.prev {
    left: 0;
}

.testimonial-nav.next {
    right: 0;
}

.testimonial-content {
    flex: 1;
    padding: 40px 20px;
}

.testimonial-stars {
    margin-bottom: 30px;
}

.star {
    color: #F0B21B;
    font-size: 1.2rem;
    margin: 0 2px;
}

.testimonial-avatar {
    margin-bottom: 30px;
}

.avatar-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #F0B21B, #e6a632);
    margin: 0 auto;
    border: 4px solid #F0B21B;
    position: relative;
}

.avatar-circle::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--white);
    border: 2px solid #F0B21B;
}

.testimonial-quote {
    margin-bottom: 30px;
    position: relative;
}

.quote-mark {
    font-size: 4rem;
    color: #F0B21B;
    font-family: serif;
    line-height: 1;
    display: block;
    margin-bottom: 20px;
}

.testimonial-quote p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-light);
    font-style: italic;
    max-width: 600px;
    margin: 0 auto;
}

.testimonial-author h4 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 5px;
    font-weight: 600;
}

.testimonial-author p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
}

.testimonials-indicators {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.testimonials-indicators .indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e9ecef;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.testimonials-indicators .indicator.active {
    background: #F0B21B;
}

/* Testimonials Responsive Design */
@media (max-width: 768px) {
    .testimonials {
        padding: 60px 0;
    }

    .testimonials h2 {
        font-size: 2rem;
    }

    .testimonials-slider {
        padding: 0 40px;
    }

    .testimonial-nav {
        font-size: 1.5rem;
        padding: 8px;
    }

    .testimonial-content {
        padding: 30px 10px;
    }

    .avatar-circle {
        width: 60px;
        height: 60px;
    }

    .avatar-circle::after {
        width: 40px;
        height: 40px;
    }

    .quote-mark {
        font-size: 3rem;
        margin-bottom: 15px;
    }

    .testimonial-quote p {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .testimonials-slider {
        padding: 0 20px;
    }

    .testimonial-nav {
        position: relative;
        margin: 0 10px;
    }

    .testimonials-slider {
        flex-direction: column;
        gap: 20px;
    }

    .testimonial-nav.prev,
    .testimonial-nav.next {
        position: relative;
        left: auto;
        right: auto;
    }
}

/* About Page Styles */
.about-page {
    padding: 40px 0;
}

.breadcrumb {
    margin-bottom: 40px;
    font-size: 0.9rem;
    color: var(--text-light);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb span {
    color: var(--text-dark);
    font-weight: 500;
}

/* Our Story Section */
.our-story-section {
    padding: 60px 0;
    text-align: center;
}

.story-header {
    margin-bottom: 80px;
}

.story-header h1 {
    font-size: 3rem;
    color: var(--text-dark);
    margin-bottom: 20px;
    font-weight: 600;
}

.story-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    text-align: left;
}

.story-left h2 {
    font-size: 2.2rem;
    color: var(--text-dark);
    margin-bottom: 20px;
    font-weight: 600;
}

.story-left p {
    font-size: 1rem;
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 40px;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.stat-item {
    background: var(--background-light);
    padding: 30px 20px;
    border-radius: 8px;
    text-align: center;
}

.stat-item h3 {
    font-size: 2.5rem;
    color: #F0B21B;
    margin-bottom: 10px;
    font-weight: bold;
}

.stat-item p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
}

.story-image {
    width: 100%;
    height: 400px;
    border-radius: 8px;
    overflow: hidden;
    background: #e9ecef;
}

.story-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Our Mission Section */
.our-mission-section {
    padding: 80px 0;
    background: var(--background-light);
}

.mission-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.mission-image {
    width: 100%;
    height: 400px;
    border-radius: 8px;
    overflow: hidden;
    background: #e9ecef;
}

.mission-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.mission-right h2 {
    font-size: 2.2rem;
    color: var(--text-dark);
    margin-bottom: 20px;
    font-weight: 600;
}

.mission-right p {
    font-size: 1rem;
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 30px;
}

.mission-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.feature-icon {
    width: 24px;
    height: 24px;
    background: #F0B21B;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
    flex-shrink: 0;
}

.feature-item span {
    font-size: 1rem;
    color: var(--text-dark);
    font-weight: 500;
}

/* Our Values Section */
.our-values-section {
    padding: 80px 0;
    background: var(--background-light);
}

.values-header {
    text-align: center;
    margin-bottom: 60px;
}

.values-header h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    font-weight: 600;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
}

.value-card {
    background: var(--white);
    padding: 40px 30px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.value-icon {
    width: 60px;
    height: 60px;
    background: rgba(240, 178, 27, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px auto;
}

.value-card h3 {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin-bottom: 15px;
    font-weight: 600;
}

.value-card p {
    font-size: 0.9rem;
    color: var(--text-light);
    line-height: 1.5;
    margin: 0;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 20px;
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: color 0.3s;
}

.view-all:hover {
    color: var(--primary-dark);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.category-card {
    background: var(--white);
    padding: 40px 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    position: relative;
    overflow: hidden;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    text-decoration: none;
    color: inherit;
}



.category-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.category-card h3 {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.category-card p {
    color: var(--text-light);
    font-size: 14px;
    margin: 0;
}

.contact-section {
    background: var(--background-light);
    padding: 80px 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-info h2 {
    font-size: 2.2rem;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.contact-info p {
    color: var(--text-light);
    margin-bottom: 40px;
    font-size: 1.1rem;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 15px;
}

.method-icon {
    background: var(--primary-color);
    color: var(--white);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.contact-method h4 {
    color: var(--text-dark);
    margin-bottom: 5px;
}

.contact-method p {
    color: var(--text-light);
    margin: 0;
}

.contact-form {
    background: var(--white);
    padding: 40px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

/* Product Catalog Page */
.catalog-page {
    padding: 2rem 0;
}

.catalog-header {
    margin-bottom: 2rem;
}

.breadcrumb {
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb span {
    color: var(--text-light);
}

.catalog-header h1 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.catalog-header p {
    color: var(--text-light);
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.category-actions {
    margin-top: 1rem;
}

.clear-filter-btn {
    background: var(--background-light);
    color: var(--text-dark);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-size: 0.9rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.clear-filter-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    text-decoration: none;
}

.catalog-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
}

.catalog-sidebar {
    background: var(--white);
    border-radius: 12px;
    padding: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    height: fit-content;
    border: 1px solid #f0f0f0;
}

/* Quick Filters Section */
.quick-filters {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    margin-top: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
}

.quick-filters h3 {
    margin: 0 0 1.5rem 0;
    color: var(--text-dark);
    font-size: 1.4rem;
    font-weight: 600;
    letter-spacing: -0.02em;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.quick-filters .filter-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.quick-filters .filter-group label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    color: var(--text-dark);
}

.quick-filters .filter-group label:hover {
    background: #f8f9fa;
}

.quick-filters .filter-group input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    accent-color: #F0B21B;
    cursor: pointer;
}

/* Price Range Section */
.price-range-section {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    margin-top: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
}

.price-range-section h3 {
    margin: 0 0 1.5rem 0;
    color: var(--text-dark);
    font-size: 1.4rem;
    font-weight: 600;
    letter-spacing: -0.02em;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.price-range-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.price-range-options label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    color: var(--text-dark);
}

.price-range-options label:hover {
    background: #f8f9fa;
}

.price-range-options input[type="radio"] {
    width: 1.25rem;
    height: 1.25rem;
    accent-color: #F0B21B;
    cursor: pointer;
}

/* 3D Configurator Section */
.configurator-section {
    background: linear-gradient(135deg, #F0B21B 0%, #d49a16 100%);
    border-radius: 12px;
    padding: 2rem;
    margin-top: 1.5rem;
    color: white;
    text-align: center;
}

.configurator-section h3 {
    margin: 0 0 1rem 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.configurator-section p {
    margin: 0 0 1.5rem 0;
    opacity: 0.9;
    line-height: 1.5;
}

.configurator-section .btn {
    background: white;
    color: #F0B21B;
    border: none;
    padding: 0.875rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.configurator-section .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    color: #d49a16;
}

.catalog-main {
    background: var(--white);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.catalog-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.results-info {
    color: var(--text-light);
    font-size: 0.9rem;
}

.catalog-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.catalog-actions .search-box {
    flex: 1;
    max-width: 300px;
}

.catalog-actions .search-box input {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #fafafa;
}

.catalog-actions .search-box input:focus {
    outline: none;
    border-color: #F0B21B;
    background: var(--white);
    box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
    transform: translateY(-1px);
}

.catalog-actions .search-box input:hover {
    border-color: #d1d5db;
    background: var(--white);
}

.catalog-actions .sort-select {
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    background: #fafafa;
    cursor: pointer;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 3rem;
}

.catalog-actions .sort-select:focus {
    outline: none;
    border-color: #F0B21B;
    background-color: var(--white);
    box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.catalog-actions .sort-select:hover {
    border-color: #d1d5db;
    background-color: var(--white);
}

.catalog-actions .view-toggle {
    display: flex;
    gap: 0.5rem;
}

.catalog-actions .view-btn {
    padding: 0.875rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background: #fafafa;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.catalog-actions .view-btn:hover {
    border-color: #F0B21B;
    background: var(--white);
}

.catalog-actions .view-btn.active {
    background: #F0B21B;
    border-color: #F0B21B;
    color: white;
}

.catalog-actions .view-btn.active:hover {
    background: #d49a16;
    border-color: #d49a16;
}

.search-box input {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    width: 200px;
}

.sort-select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.view-toggle {
    display: flex;
    gap: 0.25rem;
}

.view-btn {
    background: var(--background-light);
    border: 1px solid var(--border-color);
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 4px;
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
}

.no-products {
    text-align: center;
    padding: 3rem;
    color: var(--text-light);
}

/* Auth Pages */
.auth-page {
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.auth-container {
    max-width: 400px;
    width: 100%;
}

.auth-form {
    background: var(--white);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.auth-form h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.auth-switch {
    text-align: center;
    margin-top: 1rem;
}

.link-button {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    text-decoration: underline;
}

.back-to-home {
    text-align: center;
    margin-top: 1rem;
}

.back-to-home a {
    color: var(--text-light);
    text-decoration: none;
}

/* Payment Methods Page */
.payment-methods-page {
    background: var(--background-light);
    min-height: 100vh;
    padding: 3rem 0;
}

.payment-header {
    text-align: center;
    margin-bottom: 3rem;
}

.payment-header h1 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-weight: 600;
}

.header-underline {
    width: 80px;
    height: 4px;
    background: #F0B21B;
    margin: 0 auto 1.5rem auto;
    border-radius: 2px;
}

.payment-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.payment-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
    background: white;
    border-radius: 8px;
    padding: 8px;
    box-shadow: var(--shadow);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 3rem;
}

.payment-tab {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: var(--text-light);
    font-weight: 500;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.payment-tab.active {
    background: #F0B21B;
    color: white;
    font-weight: 600;
}

.payment-tab:hover:not(.active) {
    background: var(--background-light);
    color: var(--text-dark);
}

.payment-content {
    max-width: 1000px;
    margin: 0 auto;
}

.payment-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    background: white;
    border-radius: 12px;
    padding: 2.5rem;
    box-shadow: var(--shadow);
}

.payment-main h2 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-weight: 600;
}

.payment-main p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 1rem;
}

.payment-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
}

.feature-icon {
    font-size: 1.2rem;
}

.feature-item span:last-child {
    color: var(--text-dark);
    font-weight: 500;
}

.security-features {
    background: var(--background-light);
    border-radius: 8px;
    padding: 1.5rem;
}

.security-features h3 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.security-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.security-item:last-child {
    margin-bottom: 0;
}

.security-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: rgba(240, 178, 27, 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.security-info h4 {
    font-size: 1rem;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.security-info p {
    font-size: 0.9rem;
    color: var(--text-light);
    line-height: 1.4;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .payment-methods-page {
        padding: 2rem 0;
    }

    .payment-header h1 {
        font-size: 2rem;
    }

    .payment-tabs {
        flex-direction: column;
        max-width: none;
        margin: 0 1rem 2rem 1rem;
    }

    .payment-tab {
        text-align: center;
        padding: 15px;
    }

    .payment-section {
        grid-template-columns: 1fr;
        gap: 2rem;
        margin: 0 1rem;
        padding: 1.5rem;
    }

    .payment-features {
        grid-template-columns: 1fr;
    }

    .security-features {
        order: -1;
    }
}

/* Design Philosophy Section */
.design-philosophy-section {
    padding: 80px 0;
    background: #f8f9fa;
    margin-top: 60px;
}

.philosophy-header {
    text-align: center;
    margin-bottom: 60px;
}

.philosophy-header h2 {
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 1rem;
    color: #333;
}

.philosophy-header p {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.philosophy-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.philosophy-left h3 {
    font-size: 2rem;
    font-weight: 400;
    margin-bottom: 1.5rem;
    color: #333;
}

.philosophy-left > p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #666;
    margin-bottom: 2rem;
}

.typography-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.typo-feature {
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.typo-feature h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #F0B21B;
}

.typo-feature p {
    font-size: 1rem;
    color: #666;
    line-height: 1.6;
    margin: 0;
}

.font-showcase {
    background: white;
    padding: 3rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.font-example {
    margin-bottom: 2.5rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #eee;
}

.font-example:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

/* Font Display - This bold 700 weight is referenced in Gallery page title */
.font-display {
    font-size: 2.5rem;
    font-weight: 700; /* Same weight used in Gallery title for consistency */
    color: #333;
    margin-bottom: 0.5rem;
    line-height: 1.2;
    letter-spacing: -0.02em; /* Modern tight spacing */
}

.font-subtitle {
    font-size: 1.5rem;
    font-weight: 400;
    color: #555;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.font-body {
    font-size: 1rem;
    line-height: 1.6;
    color: #666;
    margin-bottom: 0.5rem;
}

.font-label {
    font-size: 0.8rem;
    color: #999;
    font-style: italic;
    margin: 0;
}

/* About Page Responsive Design */
@media (max-width: 768px) {
    .story-header h1 {
        font-size: 2.5rem;
    }

    .philosophy-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .philosophy-header h2 {
        font-size: 2rem;
    }

    .philosophy-left h3 {
        font-size: 1.5rem;
    }

    .font-showcase {
        padding: 2rem;
    }

    .font-display {
        font-size: 2rem;
    }

    .font-subtitle {
        font-size: 1.2rem;
    }

    .story-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .mission-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .values-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .story-image,
    .mission-image {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .story-header h1 {
        font-size: 2rem;
    }

    .story-left h2,
    .mission-right h2 {
        font-size: 1.8rem;
    }

    .values-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .value-card {
        padding: 30px 20px;
    }

    .story-image,
    .mission-image {
        height: 250px;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-text h1 {
        font-size: 2.5rem;
    }

    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .catalog-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .catalog-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .catalog-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .catalog-actions .search-box {
        max-width: none;
    }

    .catalog-actions .search-box input,
    .catalog-actions .sort-select {
        padding: 0.875rem;
        font-size: 0.9rem;
    }

    .catalog-actions .view-toggle {
        justify-content: center;
    }

    .quick-filters,
    .price-range-section,
    .configurator-section {
        padding: 1.5rem;
        margin-top: 1rem;
    }

    .quick-filters h3,
    .price-range-section h3 {
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }

    .configurator-section h3 {
        font-size: 1.1rem;
    }

    .search-box input {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 60px 0;
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .category-card {
        padding: 30px 15px;
    }

    .contact-form {
        padding: 25px;
    }
}
