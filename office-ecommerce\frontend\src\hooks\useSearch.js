import { useState, useCallback, useRef } from 'react';
import { getProducts } from '../services/products';

export const useSearch = () => {
    const [searchResults, setSearchResults] = useState([]);
    const [isSearching, setIsSearching] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [searchCache, setSearchCache] = useState(new Map());
    const debounceRef = useRef(null);

    // Advanced search function with multiple criteria
    const searchProducts = useCallback(async (query, filters = {}) => {
        if (!query || query.length < 2) {
            setSearchResults([]);
            return [];
        }

        const cacheKey = `${query.toLowerCase()}_${JSON.stringify(filters)}`;
        
        // Check cache first
        if (searchCache.has(cacheKey)) {
            const cachedResults = searchCache.get(cacheKey);
            setSearchResults(cachedResults);
            return cachedResults;
        }

        setIsSearching(true);
        
        try {
            const products = await getProducts();
            const filteredProducts = filterProducts(products, query, filters);
            
            // Cache the results
            setSearchCache(prev => {
                const newCache = new Map(prev);
                newCache.set(cacheKey, filteredProducts);
                
                // Limit cache size
                if (newCache.size > 100) {
                    const firstKey = newCache.keys().next().value;
                    newCache.delete(firstKey);
                }
                
                return newCache;
            });
            
            setSearchResults(filteredProducts);
            return filteredProducts;
        } catch (error) {
            console.error('Search error:', error);
            setSearchResults([]);
            return [];
        } finally {
            setIsSearching(false);
        }
    }, [searchCache]);

    // Debounced search
    const debouncedSearch = useCallback((query, filters = {}, delay = 300) => {
        if (debounceRef.current) {
            clearTimeout(debounceRef.current);
        }
        
        debounceRef.current = setTimeout(() => {
            searchProducts(query, filters);
        }, delay);
    }, [searchProducts]);

    // Filter products with advanced criteria
    const filterProducts = (products, query, filters = {}) => {
        const searchTerm = query.toLowerCase().trim();
        
        let filtered = products.filter(product => {
            // Text-based search
            const name = product.name.toLowerCase();
            const category = product.categoryName.toLowerCase();
            const description = product.description.toLowerCase();
            
            const textMatch = name.includes(searchTerm) || 
                            category.includes(searchTerm) || 
                            description.includes(searchTerm);
            
            if (!textMatch) return false;
            
            // Apply additional filters
            if (filters.category && product.categoryName !== filters.category) {
                return false;
            }
            
            if (filters.priceRange) {
                const [min, max] = filters.priceRange.split('-').map(Number);
                const price = product.discountPrice || product.price;
                if (max === 999999) {
                    if (price < min) return false;
                } else {
                    if (price < min || price > max) return false;
                }
            }
            
            if (filters.featured && !product.featured) {
                return false;
            }
            
            if (filters.inStock && (!product.stock || product.stock <= 0)) {
                return false;
            }
            
            return true;
        });

        // Sort results by relevance
        filtered.sort((a, b) => {
            // Calculate relevance scores
            const aScore = calculateRelevanceScore(a, searchTerm);
            const bScore = calculateRelevanceScore(b, searchTerm);
            
            if (aScore !== bScore) {
                return bScore - aScore; // Higher score first
            }
            
            // Secondary sort by name
            return a.name.localeCompare(b.name);
        });

        return filtered;
    };

    // Calculate relevance score for search results
    const calculateRelevanceScore = (product, searchTerm) => {
        let score = 0;
        const name = product.name.toLowerCase();
        const category = product.categoryName.toLowerCase();
        const description = product.description.toLowerCase();
        
        // Exact name match gets highest score
        if (name === searchTerm) score += 100;
        else if (name.startsWith(searchTerm)) score += 80;
        else if (name.includes(searchTerm)) score += 60;
        
        // Category matches
        if (category === searchTerm) score += 50;
        else if (category.includes(searchTerm)) score += 30;
        
        // Description matches
        if (description.includes(searchTerm)) score += 20;
        
        // Boost featured products
        if (product.featured) score += 10;
        
        // Boost products with discounts
        if (product.discountPrice && product.discountPrice < product.price) {
            score += 5;
        }
        
        return score;
    };

    // Get search suggestions (limited results for autocomplete)
    const getSearchSuggestions = useCallback(async (query, limit = 6) => {
        if (!query || query.length < 2) {
            return [];
        }

        const results = await searchProducts(query);
        return results.slice(0, limit);
    }, [searchProducts]);

    // Clear search results
    const clearSearch = useCallback(() => {
        setSearchResults([]);
        setSearchQuery('');
        if (debounceRef.current) {
            clearTimeout(debounceRef.current);
        }
    }, []);

    // Clear search cache
    const clearCache = useCallback(() => {
        setSearchCache(new Map());
    }, []);

    return {
        // State
        searchResults,
        isSearching,
        searchQuery,
        
        // Actions
        searchProducts,
        debouncedSearch,
        getSearchSuggestions,
        clearSearch,
        clearCache,
        setSearchQuery,
        
        // Utils
        filterProducts,
        calculateRelevanceScore
    };
};

export default useSearch;
