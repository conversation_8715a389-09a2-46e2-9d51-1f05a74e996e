import React, { useState, useRef, useEffect } from 'react';
import * as THREE from 'three';
import GLBUploader from '../3d/GLBUploader';

const GLBUploaderDemo = () => {
    const mountRef = useRef(null);
    const sceneRef = useRef(null);
    const rendererRef = useRef(null);
    const productRef = useRef(null);
    const animationIdRef = useRef(null);
    const [uploadedModel, setUploadedModel] = useState(null);

    // Initialize 3D scene
    useEffect(() => {
        if (!mountRef.current) return;

        // Scene setup
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0xf5f5f5);
        sceneRef.current = scene;

        // Camera setup
        const camera = new THREE.PerspectiveCamera(
            75,
            mountRef.current.clientWidth / mountRef.current.clientHeight,
            0.1,
            1000
        );
        camera.position.set(4, 3, 4);
        camera.lookAt(0, 0, 0);

        // Renderer setup
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        mountRef.current.appendChild(renderer.domElement);
        rendererRef.current = renderer;

        // Lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(5, 5, 5);
        directionalLight.castShadow = true;
        scene.add(directionalLight);

        // Create default cube
        createDefaultProduct(scene);

        // Animation loop
        const animate = () => {
            animationIdRef.current = requestAnimationFrame(animate);
            
            // Rotate the product
            if (productRef.current) {
                productRef.current.rotation.y += 0.01;
            }
            
            renderer.render(scene, camera);
        };
        animate();

        // Handle resize
        const handleResize = () => {
            if (!mountRef.current) return;
            const width = mountRef.current.clientWidth;
            const height = mountRef.current.clientHeight;
            camera.aspect = width / height;
            camera.updateProjectionMatrix();
            renderer.setSize(width, height);
        };

        window.addEventListener('resize', handleResize);

        return () => {
            window.removeEventListener('resize', handleResize);
            if (animationIdRef.current) {
                cancelAnimationFrame(animationIdRef.current);
            }
            if (mountRef.current && renderer.domElement) {
                mountRef.current.removeChild(renderer.domElement);
            }
            renderer.dispose();
        };
    }, []);

    const createDefaultProduct = (scene) => {
        // Create a simple default cube
        const geometry = new THREE.BoxGeometry(2, 2, 2);
        const material = new THREE.MeshStandardMaterial({ 
            color: 0x6B7280,
            roughness: 0.7,
            metalness: 0.1
        });
        const cube = new THREE.Mesh(geometry, material);
        cube.castShadow = true;
        cube.receiveShadow = true;
        
        productRef.current = cube;
        scene.add(cube);
    };

    const handleModelLoad = (model, modelInfo) => {
        if (model) {
            setUploadedModel({ model, info: modelInfo });
            console.log('Model loaded:', modelInfo);
        } else {
            // Model was removed, recreate default product
            setUploadedModel(null);
            if (sceneRef.current) {
                createDefaultProduct(sceneRef.current);
            }
        }
    };

    const handleUploadError = (error) => {
        console.error('Upload error:', error);
        alert(error);
    };

    return (
        <div style={{ padding: '2rem' }}>
            <h2>GLB Uploader Demo</h2>
            <p>This demo shows how to integrate the GLB uploader with a 3D scene.</p>
            
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 400px', gap: '2rem', marginTop: '2rem' }}>
                {/* 3D Viewer */}
                <div style={{ background: '#f8f9fa', borderRadius: '12px', padding: '1rem' }}>
                    <h3>3D Preview</h3>
                    <div 
                        ref={mountRef} 
                        style={{ 
                            width: '100%', 
                            height: '400px', 
                            background: '#fff', 
                            borderRadius: '8px',
                            border: '1px solid #e5e7eb'
                        }}
                    />
                </div>

                {/* Upload Panel */}
                <div>
                    <GLBUploader
                        onModelLoad={handleModelLoad}
                        onError={handleUploadError}
                        currentScene={sceneRef.current}
                        productRef={productRef}
                    />
                    
                    {uploadedModel && (
                        <div style={{ 
                            marginTop: '1rem', 
                            padding: '1rem', 
                            background: '#d4edda', 
                            borderRadius: '8px',
                            border: '1px solid #c3e6cb'
                        }}>
                            <h4 style={{ margin: '0 0 0.5rem 0', color: '#155724' }}>
                                Model Successfully Loaded!
                            </h4>
                            <p style={{ margin: 0, color: '#155724', fontSize: '0.9rem' }}>
                                Your custom 3D model is now displayed in the viewer above.
                            </p>
                        </div>
                    )}

                    <div style={{ 
                        marginTop: '1rem', 
                        padding: '1rem', 
                        background: '#e3f2fd', 
                        borderRadius: '8px',
                        border: '1px solid #bbdefb'
                    }}>
                        <h4 style={{ margin: '0 0 0.5rem 0', color: '#1565c0' }}>
                            How to Use:
                        </h4>
                        <ol style={{ margin: 0, paddingLeft: '1.25rem', color: '#1565c0', fontSize: '0.9rem' }}>
                            <li>Click "Upload GLB/GLTF" button</li>
                            <li>Select your 3D model file</li>
                            <li>Wait for the upload to complete</li>
                            <li>Your model will replace the default cube</li>
                            <li>Use "Remove" to go back to default</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default GLBUploaderDemo;
