{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\pages\\\\ProductCatalog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useLocation, Link } from 'react-router-dom';\nimport ProductCard from '../components/product/ProductCard';\nimport ProductFilter from '../components/product/ProductFilter';\nimport { getAllProducts, getCategories } from '../services/products';\nimport '../styles/pages.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductCatalog = () => {\n  _s();\n  const location = useLocation();\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    category: '',\n    priceRange: '',\n    search: '',\n    sortBy: 'name'\n  });\n  const [filteredProducts, setFilteredProducts] = useState([]);\n  const [selectedCategoryName, setSelectedCategoryName] = useState('');\n\n  // Parse URL parameters\n  useEffect(() => {\n    const searchParams = new URLSearchParams(location.search);\n    const categoryParam = searchParams.get('category');\n    if (categoryParam) {\n      setFilters(prev => ({\n        ...prev,\n        category: categoryParam\n      }));\n      setSelectedCategoryName(categoryParam);\n    }\n  }, [location.search]);\n  useEffect(() => {\n    loadData();\n  }, []);\n  useEffect(() => {\n    applyFilters();\n  }, [products, filters]);\n  const loadData = async () => {\n    try {\n      const [productsResponse, categoriesResponse] = await Promise.all([getAllProducts(), getCategories()]);\n      const productsData = productsResponse.products || [];\n      const categoriesData = categoriesResponse.categories || [];\n      setProducts(productsData);\n      setCategories([{\n        id: '',\n        name: 'All Products',\n        count: productsData.length\n      }, ...categoriesData]);\n    } catch (error) {\n      console.error('Error loading data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const applyFilters = () => {\n    let filtered = [...products];\n\n    // Category filter\n    if (filters.category) {\n      filtered = filtered.filter(product => {\n        var _product$categoryId;\n        return ((_product$categoryId = product.categoryId) === null || _product$categoryId === void 0 ? void 0 : _product$categoryId.toString()) === filters.category || product.categoryName === filters.category;\n      });\n    }\n\n    // Search filter\n    if (filters.search) {\n      filtered = filtered.filter(product => product.name.toLowerCase().includes(filters.search.toLowerCase()) || product.description.toLowerCase().includes(filters.search.toLowerCase()));\n    }\n\n    // Price range filter\n    if (filters.priceRange) {\n      const [min, max] = filters.priceRange.split('-').map(Number);\n      filtered = filtered.filter(product => {\n        const price = product.discountPrice || product.price;\n        return price >= min && (max ? price <= max : true);\n      });\n    }\n\n    // Sort\n    filtered.sort((a, b) => {\n      const priceA = a.discountPrice || a.price;\n      const priceB = b.discountPrice || b.price;\n      switch (filters.sortBy) {\n        case 'price-low':\n          return priceA - priceB;\n        case 'price-high':\n          return priceB - priceA;\n        case 'name':\n        default:\n          return a.name.localeCompare(b.name);\n      }\n    });\n    setFilteredProducts(filtered);\n  };\n  const handleFilterChange = newFilters => {\n    setFilters({\n      ...filters,\n      ...newFilters\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"catalog-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 25\n          }, this), \"Loading products...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"catalog-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"catalog-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"breadcrumb\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 25\n          }, this), \" \\u203A \", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            children: \"Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 52\n          }, this), selectedCategoryName && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [\" \\u203A \", /*#__PURE__*/_jsxDEV(\"span\", {\n              children: selectedCategoryName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 34\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: selectedCategoryName ? `${selectedCategoryName} Collection` : 'Product Catalog'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: selectedCategoryName ? `Explore our premium ${selectedCategoryName.toLowerCase()} collection` : 'Discover our complete collection of premium office furniture'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 21\n        }, this), selectedCategoryName && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"category-actions\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"clear-filter-btn\",\n            children: \"View All Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"catalog-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"aside\", {\n          className: \"catalog-sidebar\",\n          children: [/*#__PURE__*/_jsxDEV(ProductFilter, {\n            categories: categories,\n            filters: filters,\n            onFilterChange: handleFilterChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quick-filters\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Quick Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 37\n                }, this), \" Featured Products\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 37\n                }, this), \" In Stock Only\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 37\n                }, this), \" 3D Customizable\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"price-range-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Price Range\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"price-range-options\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"priceRange\",\n                  value: \"all\",\n                  defaultChecked: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 37\n                }, this), \" All Prices\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"priceRange\",\n                  value: \"under-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 37\n                }, this), \" Under $500\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"priceRange\",\n                  value: \"500-1000\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 37\n                }, this), \" $500 - $1000\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"priceRange\",\n                  value: \"1000-2000\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 37\n                }, this), \" $1000 - $2000\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"priceRange\",\n                  value: \"over-2000\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 37\n                }, this), \" Over $2000\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"configurator-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"3D Configurator\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Customize furniture with our interactive 3D tool and see your design come to life\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/configurator\",\n              className: \"btn\",\n              children: \"Learn More\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"price-range-filter\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Price Range\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"price-options\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"priceRange\",\n                  value: \"\",\n                  checked: filters.priceRange === '',\n                  onChange: e => handleFilterChange({\n                    priceRange: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 37\n                }, this), \"All Prices\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"priceRange\",\n                  value: \"0-500\",\n                  checked: filters.priceRange === '0-500',\n                  onChange: e => handleFilterChange({\n                    priceRange: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 37\n                }, this), \"Under $500\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"priceRange\",\n                  value: \"500-1000\",\n                  checked: filters.priceRange === '500-1000',\n                  onChange: e => handleFilterChange({\n                    priceRange: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 37\n                }, this), \"$500 - $1000\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"priceRange\",\n                  value: \"1000-2000\",\n                  checked: filters.priceRange === '1000-2000',\n                  onChange: e => handleFilterChange({\n                    priceRange: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 37\n                }, this), \"$1000 - $2000\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"priceRange\",\n                  value: \"2000-\",\n                  checked: filters.priceRange === '2000-',\n                  onChange: e => handleFilterChange({\n                    priceRange: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 37\n                }, this), \"Over $2000\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"configurator-promo\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"promo-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"3D Configurator\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Customize furniture with our innovative 3D tool and see your design come to life\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"promo-btn\",\n                children: \"Learn More\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"catalog-main\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"catalog-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"results-info\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [filteredProducts.length, \" products found\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"catalog-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"search-box\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search products...\",\n                  value: filters.search,\n                  onChange: e => handleFilterChange({\n                    search: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.sortBy,\n                onChange: e => handleFilterChange({\n                  sortBy: e.target.value\n                }),\n                className: \"sort-select\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"name\",\n                  children: \"Sort by Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"price-low\",\n                  children: \"Price: Low to High\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"price-high\",\n                  children: \"Price: High to Low\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"view-toggle\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"view-btn active\",\n                  children: \"\\u229E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"view-btn\",\n                  children: \"\\u2630\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"products-grid\",\n            children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n              product: product\n            }, product.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 25\n          }, this), filteredProducts.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-products\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"No products found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Try adjusting your filters or search terms\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 9\n  }, this);\n};\n_s(ProductCatalog, \"+WeV/UJm7CYHq1bFKh7YRApfCmM=\", false, function () {\n  return [useLocation];\n});\n_c = ProductCatalog;\nexport default ProductCatalog;\nvar _c;\n$RefreshReg$(_c, \"ProductCatalog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useLocation", "Link", "ProductCard", "ProductFilter", "getAllProducts", "getCategories", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductCatalog", "_s", "location", "products", "setProducts", "categories", "setCategories", "loading", "setLoading", "filters", "setFilters", "category", "priceRange", "search", "sortBy", "filteredProducts", "setFilteredProducts", "selectedCategoryName", "setSelectedCategoryName", "searchParams", "URLSearchParams", "categoryParam", "get", "prev", "loadData", "applyFilters", "productsResponse", "categoriesResponse", "Promise", "all", "productsData", "categoriesData", "id", "name", "count", "length", "error", "console", "filtered", "filter", "product", "_product$categoryId", "categoryId", "toString", "categoryName", "toLowerCase", "includes", "description", "min", "max", "split", "map", "Number", "price", "discountPrice", "sort", "a", "b", "priceA", "priceB", "localeCompare", "handleFilterChange", "newFilters", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onFilterChange", "type", "value", "defaultChecked", "checked", "onChange", "e", "target", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/pages/ProductCatalog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useLocation, Link } from 'react-router-dom';\r\nimport ProductCard from '../components/product/ProductCard';\r\nimport ProductFilter from '../components/product/ProductFilter';\r\nimport { getAllProducts, getCategories } from '../services/products';\r\nimport '../styles/pages.css';\r\n\r\nconst ProductCatalog = () => {\r\n    const location = useLocation();\r\n    const [products, setProducts] = useState([]);\r\n    const [categories, setCategories] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [filters, setFilters] = useState({\r\n        category: '',\r\n        priceRange: '',\r\n        search: '',\r\n        sortBy: 'name'\r\n    });\r\n    const [filteredProducts, setFilteredProducts] = useState([]);\r\n    const [selectedCategoryName, setSelectedCategoryName] = useState('');\r\n\r\n    // Parse URL parameters\r\n    useEffect(() => {\r\n        const searchParams = new URLSearchParams(location.search);\r\n        const categoryParam = searchParams.get('category');\r\n\r\n        if (categoryParam) {\r\n            setFilters(prev => ({\r\n                ...prev,\r\n                category: categoryParam\r\n            }));\r\n            setSelectedCategoryName(categoryParam);\r\n        }\r\n    }, [location.search]);\r\n\r\n    useEffect(() => {\r\n        loadData();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        applyFilters();\r\n    }, [products, filters]);\r\n\r\n    const loadData = async () => {\r\n        try {\r\n            const [productsResponse, categoriesResponse] = await Promise.all([\r\n                getAllProducts(),\r\n                getCategories()\r\n            ]);\r\n            const productsData = productsResponse.products || [];\r\n            const categoriesData = categoriesResponse.categories || [];\r\n            setProducts(productsData);\r\n            setCategories([\r\n                { id: '', name: 'All Products', count: productsData.length },\r\n                ...categoriesData\r\n            ]);\r\n        } catch (error) {\r\n            console.error('Error loading data:', error);\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const applyFilters = () => {\r\n        let filtered = [...products];\r\n\r\n        // Category filter\r\n        if (filters.category) {\r\n            filtered = filtered.filter(product =>\r\n                product.categoryId?.toString() === filters.category ||\r\n                product.categoryName === filters.category\r\n            );\r\n        }\r\n\r\n        // Search filter\r\n        if (filters.search) {\r\n            filtered = filtered.filter(product =>\r\n                product.name.toLowerCase().includes(filters.search.toLowerCase()) ||\r\n                product.description.toLowerCase().includes(filters.search.toLowerCase())\r\n            );\r\n        }\r\n\r\n        // Price range filter\r\n        if (filters.priceRange) {\r\n            const [min, max] = filters.priceRange.split('-').map(Number);\r\n            filtered = filtered.filter(product => {\r\n                const price = product.discountPrice || product.price;\r\n                return price >= min && (max ? price <= max : true);\r\n            });\r\n        }\r\n\r\n        // Sort\r\n        filtered.sort((a, b) => {\r\n            const priceA = a.discountPrice || a.price;\r\n            const priceB = b.discountPrice || b.price;\r\n\r\n            switch (filters.sortBy) {\r\n                case 'price-low':\r\n                    return priceA - priceB;\r\n                case 'price-high':\r\n                    return priceB - priceA;\r\n                case 'name':\r\n                default:\r\n                    return a.name.localeCompare(b.name);\r\n            }\r\n        });\r\n\r\n        setFilteredProducts(filtered);\r\n    };\r\n\r\n    const handleFilterChange = (newFilters) => {\r\n        setFilters({ ...filters, ...newFilters });\r\n    };\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className=\"catalog-page\">\r\n                <div className=\"container\">\r\n                    <div className=\"loading\">\r\n                        <div className=\"spinner\"></div>\r\n                        Loading products...\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"catalog-page\">\r\n            <div className=\"container\">\r\n                <div className=\"catalog-header\">\r\n                    <div className=\"breadcrumb\">\r\n                        <Link to=\"/\">Home</Link> › <Link to=\"/products\">Products</Link>\r\n                        {selectedCategoryName && (\r\n                            <> › <span>{selectedCategoryName}</span></>\r\n                        )}\r\n                    </div>\r\n                    <h1>\r\n                        {selectedCategoryName ? `${selectedCategoryName} Collection` : 'Product Catalog'}\r\n                    </h1>\r\n                    <p>\r\n                        {selectedCategoryName\r\n                            ? `Explore our premium ${selectedCategoryName.toLowerCase()} collection`\r\n                            : 'Discover our complete collection of premium office furniture'\r\n                        }\r\n                    </p>\r\n                    {selectedCategoryName && (\r\n                        <div className=\"category-actions\">\r\n                            <Link to=\"/products\" className=\"clear-filter-btn\">\r\n                                View All Products\r\n                            </Link>\r\n                        </div>\r\n                    )}\r\n                </div>\r\n\r\n                <div className=\"catalog-content\">\r\n                    <aside className=\"catalog-sidebar\">\r\n                        <ProductFilter\r\n                            categories={categories}\r\n                            filters={filters}\r\n                            onFilterChange={handleFilterChange}\r\n                        />\r\n\r\n                        {/* Quick Filters */}\r\n                        <div className=\"quick-filters\">\r\n                            <h3>Quick Filters</h3>\r\n                            <div className=\"filter-group\">\r\n                                <label>\r\n                                    <input type=\"checkbox\" /> Featured Products\r\n                                </label>\r\n                                <label>\r\n                                    <input type=\"checkbox\" /> In Stock Only\r\n                                </label>\r\n                                <label>\r\n                                    <input type=\"checkbox\" /> 3D Customizable\r\n                                </label>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Price Range */}\r\n                        <div className=\"price-range-section\">\r\n                            <h3>Price Range</h3>\r\n                            <div className=\"price-range-options\">\r\n                                <label>\r\n                                    <input type=\"radio\" name=\"priceRange\" value=\"all\" defaultChecked /> All Prices\r\n                                </label>\r\n                                <label>\r\n                                    <input type=\"radio\" name=\"priceRange\" value=\"under-500\" /> Under $500\r\n                                </label>\r\n                                <label>\r\n                                    <input type=\"radio\" name=\"priceRange\" value=\"500-1000\" /> $500 - $1000\r\n                                </label>\r\n                                <label>\r\n                                    <input type=\"radio\" name=\"priceRange\" value=\"1000-2000\" /> $1000 - $2000\r\n                                </label>\r\n                                <label>\r\n                                    <input type=\"radio\" name=\"priceRange\" value=\"over-2000\" /> Over $2000\r\n                                </label>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* 3D Configurator */}\r\n                        <div className=\"configurator-section\">\r\n                            <h3>3D Configurator</h3>\r\n                            <p>Customize furniture with our interactive 3D tool and see your design come to life</p>\r\n                            <Link to=\"/configurator\" className=\"btn\">\r\n                                Learn More\r\n                            </Link>\r\n                        </div>\r\n\r\n                        {/* Price Range */}\r\n                        <div className=\"price-range-filter\">\r\n                            <h3>Price Range</h3>\r\n                            <div className=\"price-options\">\r\n                                <label>\r\n                                    <input\r\n                                        type=\"radio\"\r\n                                        name=\"priceRange\"\r\n                                        value=\"\"\r\n                                        checked={filters.priceRange === ''}\r\n                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}\r\n                                    />\r\n                                    All Prices\r\n                                </label>\r\n                                <label>\r\n                                    <input\r\n                                        type=\"radio\"\r\n                                        name=\"priceRange\"\r\n                                        value=\"0-500\"\r\n                                        checked={filters.priceRange === '0-500'}\r\n                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}\r\n                                    />\r\n                                    Under $500\r\n                                </label>\r\n                                <label>\r\n                                    <input\r\n                                        type=\"radio\"\r\n                                        name=\"priceRange\"\r\n                                        value=\"500-1000\"\r\n                                        checked={filters.priceRange === '500-1000'}\r\n                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}\r\n                                    />\r\n                                    $500 - $1000\r\n                                </label>\r\n                                <label>\r\n                                    <input\r\n                                        type=\"radio\"\r\n                                        name=\"priceRange\"\r\n                                        value=\"1000-2000\"\r\n                                        checked={filters.priceRange === '1000-2000'}\r\n                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}\r\n                                    />\r\n                                    $1000 - $2000\r\n                                </label>\r\n                                <label>\r\n                                    <input\r\n                                        type=\"radio\"\r\n                                        name=\"priceRange\"\r\n                                        value=\"2000-\"\r\n                                        checked={filters.priceRange === '2000-'}\r\n                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}\r\n                                    />\r\n                                    Over $2000\r\n                                </label>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* 3D Configurator Promo */}\r\n                        <div className=\"configurator-promo\">\r\n                            <div className=\"promo-content\">\r\n                                <h3>3D Configurator</h3>\r\n                                <p>Customize furniture with our innovative 3D tool and see your design come to life</p>\r\n                                <button className=\"promo-btn\">Learn More</button>\r\n                            </div>\r\n                        </div>\r\n                    </aside>\r\n\r\n                    <main className=\"catalog-main\">\r\n                        <div className=\"catalog-controls\">\r\n                            <div className=\"results-info\">\r\n                                <span>{filteredProducts.length} products found</span>\r\n                            </div>\r\n\r\n                            <div className=\"catalog-actions\">\r\n                                <div className=\"search-box\">\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        placeholder=\"Search products...\"\r\n                                        value={filters.search}\r\n                                        onChange={(e) => handleFilterChange({ search: e.target.value })}\r\n                                    />\r\n                                </div>\r\n\r\n                                <select\r\n                                    value={filters.sortBy}\r\n                                    onChange={(e) => handleFilterChange({ sortBy: e.target.value })}\r\n                                    className=\"sort-select\"\r\n                                >\r\n                                    <option value=\"name\">Sort by Name</option>\r\n                                    <option value=\"price-low\">Price: Low to High</option>\r\n                                    <option value=\"price-high\">Price: High to Low</option>\r\n                                </select>\r\n\r\n                                <div className=\"view-toggle\">\r\n                                    <button className=\"view-btn active\">⊞</button>\r\n                                    <button className=\"view-btn\">☰</button>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"products-grid\">\r\n                            {filteredProducts.map(product => (\r\n                                <ProductCard key={product.id} product={product} />\r\n                            ))}\r\n                        </div>\r\n\r\n                        {filteredProducts.length === 0 && (\r\n                            <div className=\"no-products\">\r\n                                <h3>No products found</h3>\r\n                                <p>Try adjusting your filters or search terms</p>\r\n                            </div>\r\n                        )}\r\n                    </main>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ProductCatalog;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,OAAOC,WAAW,MAAM,mCAAmC;AAC3D,OAAOC,aAAa,MAAM,qCAAqC;AAC/D,SAASC,cAAc,EAAEC,aAAa,QAAQ,sBAAsB;AACpE,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC;IACnCuB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC6B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;;EAEpE;EACAC,SAAS,CAAC,MAAM;IACZ,MAAM8B,YAAY,GAAG,IAAIC,eAAe,CAAClB,QAAQ,CAACW,MAAM,CAAC;IACzD,MAAMQ,aAAa,GAAGF,YAAY,CAACG,GAAG,CAAC,UAAU,CAAC;IAElD,IAAID,aAAa,EAAE;MACfX,UAAU,CAACa,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPZ,QAAQ,EAAEU;MACd,CAAC,CAAC,CAAC;MACHH,uBAAuB,CAACG,aAAa,CAAC;IAC1C;EACJ,CAAC,EAAE,CAACnB,QAAQ,CAACW,MAAM,CAAC,CAAC;EAErBxB,SAAS,CAAC,MAAM;IACZmC,QAAQ,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAENnC,SAAS,CAAC,MAAM;IACZoC,YAAY,CAAC,CAAC;EAClB,CAAC,EAAE,CAACtB,QAAQ,EAAEM,OAAO,CAAC,CAAC;EAEvB,MAAMe,QAAQ,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACA,MAAM,CAACE,gBAAgB,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC7DnC,cAAc,CAAC,CAAC,EAChBC,aAAa,CAAC,CAAC,CAClB,CAAC;MACF,MAAMmC,YAAY,GAAGJ,gBAAgB,CAACvB,QAAQ,IAAI,EAAE;MACpD,MAAM4B,cAAc,GAAGJ,kBAAkB,CAACtB,UAAU,IAAI,EAAE;MAC1DD,WAAW,CAAC0B,YAAY,CAAC;MACzBxB,aAAa,CAAC,CACV;QAAE0B,EAAE,EAAE,EAAE;QAAEC,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAEJ,YAAY,CAACK;MAAO,CAAC,EAC5D,GAAGJ,cAAc,CACpB,CAAC;IACN,CAAC,CAAC,OAAOK,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACN5B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMiB,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAIa,QAAQ,GAAG,CAAC,GAAGnC,QAAQ,CAAC;;IAE5B;IACA,IAAIM,OAAO,CAACE,QAAQ,EAAE;MAClB2B,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,OAAO;QAAA,IAAAC,mBAAA;QAAA,OAC9B,EAAAA,mBAAA,GAAAD,OAAO,CAACE,UAAU,cAAAD,mBAAA,uBAAlBA,mBAAA,CAAoBE,QAAQ,CAAC,CAAC,MAAKlC,OAAO,CAACE,QAAQ,IACnD6B,OAAO,CAACI,YAAY,KAAKnC,OAAO,CAACE,QAAQ;MAAA,CAC7C,CAAC;IACL;;IAEA;IACA,IAAIF,OAAO,CAACI,MAAM,EAAE;MAChByB,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,OAAO,IAC9BA,OAAO,CAACP,IAAI,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrC,OAAO,CAACI,MAAM,CAACgC,WAAW,CAAC,CAAC,CAAC,IACjEL,OAAO,CAACO,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrC,OAAO,CAACI,MAAM,CAACgC,WAAW,CAAC,CAAC,CAC3E,CAAC;IACL;;IAEA;IACA,IAAIpC,OAAO,CAACG,UAAU,EAAE;MACpB,MAAM,CAACoC,GAAG,EAAEC,GAAG,CAAC,GAAGxC,OAAO,CAACG,UAAU,CAACsC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;MAC5Dd,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAI;QAClC,MAAMa,KAAK,GAAGb,OAAO,CAACc,aAAa,IAAId,OAAO,CAACa,KAAK;QACpD,OAAOA,KAAK,IAAIL,GAAG,KAAKC,GAAG,GAAGI,KAAK,IAAIJ,GAAG,GAAG,IAAI,CAAC;MACtD,CAAC,CAAC;IACN;;IAEA;IACAX,QAAQ,CAACiB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACpB,MAAMC,MAAM,GAAGF,CAAC,CAACF,aAAa,IAAIE,CAAC,CAACH,KAAK;MACzC,MAAMM,MAAM,GAAGF,CAAC,CAACH,aAAa,IAAIG,CAAC,CAACJ,KAAK;MAEzC,QAAQ5C,OAAO,CAACK,MAAM;QAClB,KAAK,WAAW;UACZ,OAAO4C,MAAM,GAAGC,MAAM;QAC1B,KAAK,YAAY;UACb,OAAOA,MAAM,GAAGD,MAAM;QAC1B,KAAK,MAAM;QACX;UACI,OAAOF,CAAC,CAACvB,IAAI,CAAC2B,aAAa,CAACH,CAAC,CAACxB,IAAI,CAAC;MAC3C;IACJ,CAAC,CAAC;IAEFjB,mBAAmB,CAACsB,QAAQ,CAAC;EACjC,CAAC;EAED,MAAMuB,kBAAkB,GAAIC,UAAU,IAAK;IACvCpD,UAAU,CAAC;MAAE,GAAGD,OAAO;MAAE,GAAGqD;IAAW,CAAC,CAAC;EAC7C,CAAC;EAED,IAAIvD,OAAO,EAAE;IACT,oBACIV,OAAA;MAAKkE,SAAS,EAAC,cAAc;MAAAC,QAAA,eACzBnE,OAAA;QAAKkE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBnE,OAAA;UAAKkE,SAAS,EAAC,SAAS;UAAAC,QAAA,gBACpBnE,OAAA;YAAKkE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,uBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIvE,OAAA;IAAKkE,SAAS,EAAC,cAAc;IAAAC,QAAA,eACzBnE,OAAA;MAAKkE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtBnE,OAAA;QAAKkE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BnE,OAAA;UAAKkE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBnE,OAAA,CAACN,IAAI;YAAC8E,EAAE,EAAC,GAAG;YAAAL,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,YAAG,eAAAvE,OAAA,CAACN,IAAI;YAAC8E,EAAE,EAAC,WAAW;YAAAL,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC9DnD,oBAAoB,iBACjBpB,OAAA,CAAAE,SAAA;YAAAiE,QAAA,GAAE,UAAG,eAAAnE,OAAA;cAAAmE,QAAA,EAAO/C;YAAoB;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,eAAE,CAC7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACNvE,OAAA;UAAAmE,QAAA,EACK/C,oBAAoB,GAAG,GAAGA,oBAAoB,aAAa,GAAG;QAAiB;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eACLvE,OAAA;UAAAmE,QAAA,EACK/C,oBAAoB,GACf,uBAAuBA,oBAAoB,CAAC4B,WAAW,CAAC,CAAC,aAAa,GACtE;QAA8D;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErE,CAAC,EACHnD,oBAAoB,iBACjBpB,OAAA;UAAKkE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC7BnE,OAAA,CAACN,IAAI;YAAC8E,EAAE,EAAC,WAAW;YAACN,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAElD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAENvE,OAAA;QAAKkE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BnE,OAAA;UAAOkE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BnE,OAAA,CAACJ,aAAa;YACVY,UAAU,EAAEA,UAAW;YACvBI,OAAO,EAAEA,OAAQ;YACjB6D,cAAc,EAAET;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAGFvE,OAAA;YAAKkE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1BnE,OAAA;cAAAmE,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBvE,OAAA;cAAKkE,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBnE,OAAA;gBAAAmE,QAAA,gBACInE,OAAA;kBAAO0E,IAAI,EAAC;gBAAU;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sBAC7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBAAAmE,QAAA,gBACInE,OAAA;kBAAO0E,IAAI,EAAC;gBAAU;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,kBAC7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBAAAmE,QAAA,gBACInE,OAAA;kBAAO0E,IAAI,EAAC;gBAAU;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oBAC7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNvE,OAAA;YAAKkE,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAChCnE,OAAA;cAAAmE,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBvE,OAAA;cAAKkE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAChCnE,OAAA;gBAAAmE,QAAA,gBACInE,OAAA;kBAAO0E,IAAI,EAAC,OAAO;kBAACtC,IAAI,EAAC,YAAY;kBAACuC,KAAK,EAAC,KAAK;kBAACC,cAAc;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBAAAmE,QAAA,gBACInE,OAAA;kBAAO0E,IAAI,EAAC,OAAO;kBAACtC,IAAI,EAAC,YAAY;kBAACuC,KAAK,EAAC;gBAAW;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBAAAmE,QAAA,gBACInE,OAAA;kBAAO0E,IAAI,EAAC,OAAO;kBAACtC,IAAI,EAAC,YAAY;kBAACuC,KAAK,EAAC;gBAAU;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAC7D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBAAAmE,QAAA,gBACInE,OAAA;kBAAO0E,IAAI,EAAC,OAAO;kBAACtC,IAAI,EAAC,YAAY;kBAACuC,KAAK,EAAC;gBAAW;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,kBAC9D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBAAAmE,QAAA,gBACInE,OAAA;kBAAO0E,IAAI,EAAC,OAAO;kBAACtC,IAAI,EAAC,YAAY;kBAACuC,KAAK,EAAC;gBAAW;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNvE,OAAA;YAAKkE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjCnE,OAAA;cAAAmE,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBvE,OAAA;cAAAmE,QAAA,EAAG;YAAiF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxFvE,OAAA,CAACN,IAAI;cAAC8E,EAAE,EAAC,eAAe;cAACN,SAAS,EAAC,KAAK;cAAAC,QAAA,EAAC;YAEzC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNvE,OAAA;YAAKkE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAC/BnE,OAAA;cAAAmE,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBvE,OAAA;cAAKkE,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC1BnE,OAAA;gBAAAmE,QAAA,gBACInE,OAAA;kBACI0E,IAAI,EAAC,OAAO;kBACZtC,IAAI,EAAC,YAAY;kBACjBuC,KAAK,EAAC,EAAE;kBACRE,OAAO,EAAEjE,OAAO,CAACG,UAAU,KAAK,EAAG;kBACnC+D,QAAQ,EAAGC,CAAC,IAAKf,kBAAkB,CAAC;oBAAEjD,UAAU,EAAEgE,CAAC,CAACC,MAAM,CAACL;kBAAM,CAAC;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,cAEN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBAAAmE,QAAA,gBACInE,OAAA;kBACI0E,IAAI,EAAC,OAAO;kBACZtC,IAAI,EAAC,YAAY;kBACjBuC,KAAK,EAAC,OAAO;kBACbE,OAAO,EAAEjE,OAAO,CAACG,UAAU,KAAK,OAAQ;kBACxC+D,QAAQ,EAAGC,CAAC,IAAKf,kBAAkB,CAAC;oBAAEjD,UAAU,EAAEgE,CAAC,CAACC,MAAM,CAACL;kBAAM,CAAC;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,cAEN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBAAAmE,QAAA,gBACInE,OAAA;kBACI0E,IAAI,EAAC,OAAO;kBACZtC,IAAI,EAAC,YAAY;kBACjBuC,KAAK,EAAC,UAAU;kBAChBE,OAAO,EAAEjE,OAAO,CAACG,UAAU,KAAK,UAAW;kBAC3C+D,QAAQ,EAAGC,CAAC,IAAKf,kBAAkB,CAAC;oBAAEjD,UAAU,EAAEgE,CAAC,CAACC,MAAM,CAACL;kBAAM,CAAC;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,gBAEN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBAAAmE,QAAA,gBACInE,OAAA;kBACI0E,IAAI,EAAC,OAAO;kBACZtC,IAAI,EAAC,YAAY;kBACjBuC,KAAK,EAAC,WAAW;kBACjBE,OAAO,EAAEjE,OAAO,CAACG,UAAU,KAAK,WAAY;kBAC5C+D,QAAQ,EAAGC,CAAC,IAAKf,kBAAkB,CAAC;oBAAEjD,UAAU,EAAEgE,CAAC,CAACC,MAAM,CAACL;kBAAM,CAAC;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,iBAEN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBAAAmE,QAAA,gBACInE,OAAA;kBACI0E,IAAI,EAAC,OAAO;kBACZtC,IAAI,EAAC,YAAY;kBACjBuC,KAAK,EAAC,OAAO;kBACbE,OAAO,EAAEjE,OAAO,CAACG,UAAU,KAAK,OAAQ;kBACxC+D,QAAQ,EAAGC,CAAC,IAAKf,kBAAkB,CAAC;oBAAEjD,UAAU,EAAEgE,CAAC,CAACC,MAAM,CAACL;kBAAM,CAAC;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,cAEN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNvE,OAAA;YAAKkE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAC/BnE,OAAA;cAAKkE,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC1BnE,OAAA;gBAAAmE,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBvE,OAAA;gBAAAmE,QAAA,EAAG;cAAgF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACvFvE,OAAA;gBAAQkE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAERvE,OAAA;UAAMkE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC1BnE,OAAA;YAAKkE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC7BnE,OAAA;cAAKkE,SAAS,EAAC,cAAc;cAAAC,QAAA,eACzBnE,OAAA;gBAAAmE,QAAA,GAAOjD,gBAAgB,CAACoB,MAAM,EAAC,iBAAe;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAENvE,OAAA;cAAKkE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC5BnE,OAAA;gBAAKkE,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACvBnE,OAAA;kBACI0E,IAAI,EAAC,MAAM;kBACXO,WAAW,EAAC,oBAAoB;kBAChCN,KAAK,EAAE/D,OAAO,CAACI,MAAO;kBACtB8D,QAAQ,EAAGC,CAAC,IAAKf,kBAAkB,CAAC;oBAAEhD,MAAM,EAAE+D,CAAC,CAACC,MAAM,CAACL;kBAAM,CAAC;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENvE,OAAA;gBACI2E,KAAK,EAAE/D,OAAO,CAACK,MAAO;gBACtB6D,QAAQ,EAAGC,CAAC,IAAKf,kBAAkB,CAAC;kBAAE/C,MAAM,EAAE8D,CAAC,CAACC,MAAM,CAACL;gBAAM,CAAC,CAAE;gBAChET,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAEvBnE,OAAA;kBAAQ2E,KAAK,EAAC,MAAM;kBAAAR,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CvE,OAAA;kBAAQ2E,KAAK,EAAC,WAAW;kBAAAR,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrDvE,OAAA;kBAAQ2E,KAAK,EAAC,YAAY;kBAAAR,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eAETvE,OAAA;gBAAKkE,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACxBnE,OAAA;kBAAQkE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9CvE,OAAA;kBAAQkE,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENvE,OAAA;YAAKkE,SAAS,EAAC,eAAe;YAAAC,QAAA,EACzBjD,gBAAgB,CAACoC,GAAG,CAACX,OAAO,iBACzB3C,OAAA,CAACL,WAAW;cAAkBgD,OAAO,EAAEA;YAAQ,GAA7BA,OAAO,CAACR,EAAE;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqB,CACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAELrD,gBAAgB,CAACoB,MAAM,KAAK,CAAC,iBAC1BtC,OAAA;YAAKkE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxBnE,OAAA;cAAAmE,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BvE,OAAA;cAAAmE,QAAA,EAAG;YAA0C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACnE,EAAA,CAhUID,cAAc;EAAA,QACCV,WAAW;AAAA;AAAAyF,EAAA,GAD1B/E,cAAc;AAkUpB,eAAeA,cAAc;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}